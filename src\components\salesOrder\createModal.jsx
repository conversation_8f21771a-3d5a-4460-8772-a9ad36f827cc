import { useCallback, useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { handleFormula } from '../../helperFunction.js';
import useCompanyDetailsSelector from '../../hooks/useCompanyDetailsSelector.js';
import usePrefixIds from '../../hooks/usePrefixIds.js';
import { useGetAdditionalChargesQuery } from '../../slices/additionalChargesApiSllice.js';
import { useLinkSalesOrderMutation } from '../../slices/createPoApiSlice';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice.js';
import { useUpdateNcrMutation } from '../../slices/ncrApiSlice.js';
import {
  useCreateOrderMutation,
  useGetAllOrdersMutation,
  useUpdateOrderMutation,
} from '../../slices/orderApiSlice.js';
import { useGetQuotationsQuery } from '../../slices/quotationApiSlice';
import {
  useCreateSalesOrderMutation,
  useGetLatestSoQuery,
  useGetSalesOrderMutation,
  useUpdateSalesOrderMutation,
} from '../../slices/salesOrderSlices';
import { Store } from '../../store/Store.js';
import HsnSacModal from '../HsnSacModal.jsx';
import ProductFormatManager from '../ProductFormats/ProductFormatManager.jsx';
import { formatSalesOrderProducts } from '../ProductFormats/SalesOrderDataFormater.js';
import Button from '../global/components/Button';
import Header from '../global/components/Header.jsx';
import Attachments from './attachments';
import Charges from './charges';
import OrderDetails from './orderDetails';
import ProductTable from './productTable';

const calculateTaxAmount = (amount, percentage) =>
  (amount * (percentage || 0)) / 100;

const calculateTotalPrice = (cgst = 0, sgst = 0, igst = 0, amount) => {
  return new Promise((resolve) => {
    let localTotalPrice = 0;
    const cgstAmount = calculateTaxAmount(amount, cgst);
    const sgstAmount = calculateTaxAmount(amount, sgst);
    const igstAmount = calculateTaxAmount(amount, igst);
    localTotalPrice += amount + cgstAmount + sgstAmount + igstAmount;
    resolve({ price: localTotalPrice.toFixed(2), amount });
  });
};

const CreateModal = (props) => {
  let [searchParams] = useSearchParams();
  const navigate = useNavigate();
  // const salesOrderIdGenerator = () => {
  //   const id = props?.newId;
  //   return `SO-${id}`;
  // };
  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'voucherId',
  });
  const [createDepOrder] = useCreateOrderMutation();
  const [getAllOrders] = useGetAllOrdersMutation();
  const [additionalCharges, setAdditionalCharges] = useState({});
  const [openAddUomModal, setOpenAddUomModal] = useState(false);
  // const { data = [] } = useGetAllCompanyProfilesQuery();
  const { data: allAdditionalCharges } = useGetAdditionalChargesQuery();
  const [pdf, setPdf] = useState(props?.files ?? []);
  const [items, setItems] = useState(props?.items ?? []);
  const [selectedTermAndCondition, setSelectedTermAndCondition] = useState([]);
  const [isDefaultTermsAndConditions, setIsDefaultTermsAndConditions] =
    useState(false);
  const { data: latestSo } = useGetLatestSoQuery();
  const [isDefaultDeliveryAddress, setIsDefaultDeliveryAddress] =
    useState(false);
  const [isDefaultBillingAddress, setIsDefaultBillingAddress] = useState(false);
  const [isDefaultBankDetails, setIsDefaultBankDetails] = useState(false);
  const [createOrder, { isLoading: isCreateSalesOrderLoadig }] =
    useCreateSalesOrderMutation();
  const [updateOrder, { isLoading: isUpdateSalesOrderLoading }] =
    useUpdateSalesOrderMutation();
  const [linkSalesOrder] = useLinkSalesOrderMutation();
  const [showHsnSacModal, setShowHsnSacCodeModal] = useState(false);
  const [HsnOptions, setHsnOptions] = useState([]);
  const [showIgst, setShowIgst] = useState(false);
  const [itemsInput, setItemsInput] = useState({
    details: '',
    quantity: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    rate: 0,
    amount: 0,
    UOM: '',
    discount: 0,
    totalAmount: 0,
    hsn: '',
  });
  const [subTotal, setSubTotal] = useState(0);
  const [updateDepOrder] = useUpdateOrderMutation();
  const [input, setInput] = useState({
    CustomerData: props?.CustomerData,
    customer: props?.customer || '',
    workOrders: props?.workOrders || [],
    salesOrderDate: props?.salesOrderDate?.split('T')[0],
    madeBy: props?.madeBy || '',
    quotationID: props?.quotationID || '',
    bankDetails: '',
    termsAndConditions: props?.termsAndConditions || '',
    salesOrderStatus: props.salesOrderStatus || 'pending' || '',
    deliveryDate: props?.deliveryDate?.split('T')[0] || '',
    deliveryAddress: props?.edit
      ? [props?.deliveryAddress]
      : props?.defaultParam?.salesOrder?.deliveryAddress
        ? props?.defaultParam?.salesOrder?.deliveryAddress
        : '',
    billingAddress: props?.edit
      ? [props?.billingAddress]
      : props?.defaultParam?.salesOrder?.billingAddress
        ? props?.defaultParam?.salesOrder?.billingAddress
        : '',
    billingGST: props?.edit ? props?.billingGST : '',
    billingName: props?.edit ? props?.billingName : '',
    billingState: props?.edit ? props?.billingState : '',
    deliveryGST: props?.edit ? props?.deliveryGST : '',
    deliveryName: props?.edit ? props?.deliveryName : '',
    deliveryState: props?.edit ? props?.deliveryState : '',
    attachments: [],
    additionalFields: props?.edit ? props?.additionalFields : null,
    paymentTerm: props?.edit?.paymentTerm || '',
    deletedMedia: [],
    charges: {},
    businessDetails: {
      companyName: '',
      address: {},
      contact: '',
      gstNumber: '',
    },
    productColumnHideStatus: {},
    customColumnsHideStatus: {},
    bankDetailsHideStatus: false,
    termsAndConditionsHide: {},
    hideBillingAddress: false,
  });
  // product format
  const [tableFormatProducts, setTableFormatProducts] = useState([]);
  const [tableFormatsCharges, setTableFormatsCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);

  const [isSetAsDefault, setIsSetAsDefault] = useState(false);
  const [additionalFields, setAdditionalFields] = useState(null);
  const { defaults, state, dispatch } = useContext(Store);
  const [PaymentTermOptions, setPaymentTermOptions] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState(
    props.products ?? []
  );
  const { data: quotations } = useGetQuotationsQuery();
  const [allProductTemplates, setAllProductTemplates] = useState(
    props?.isEdit ? props?.allProductTemplates : []
  );
  const filtered = quotations?.filter((el) => {
    if (
      !defaults?.defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
        'salesOrder'
      )
    ) {
      return el?.quoteStatus.toLowerCase() === 'approved';
    } else {
      return el?.quoteStatus !== '';
    }
  });
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const quotationData = filtered?.filter((el) => {
    const bool = !props.orders.some(
      (order) => order.quotationID === el.quoteID
    );
    return bool;
  });
  const { data: dropdowns } = useGetDropdownsQuery();

  const [selectedQuotation, setSelectedQuotation] = useState(
    filtered?.find((el) => el?.quoteID === input?.quotationID)
  );
  const createdBy = state?.user?.name;
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [getSalesOrder, { data: allSalesOrder }] = useGetSalesOrderMutation();
  const [columnInputs, setColumnInputs] = useState([]);
  const [subtotalFormulaPrice, setSubtotalFormulaPrice] = useState(0);
  const pageSlug = '/salesordermanagement/orders';
  const [hidePoTable, setHidePoTable] = useState(() => {
    const savedValue = localStorage.getItem('hideProductDetailsObject');
    const parsedValue = savedValue ? JSON.parse(savedValue) : {};
    return parsedValue[pageSlug] || false;
  });
  const [additionalFieldsHideStatus, setAdditionalFieldsHideStatus] = useState(
    props?.additionalFieldsHideStatus || {}
  );

  // console.log('SELECTED QUOTATION', selectedQuotation);

  const {
    addressSelector,
    bankDetailsSelector,
    contactSelector,
    emailSelector,
    profileSelector,
    companyDetails: data,
  } = useCompanyDetailsSelector(input, setInput);

  useEffect(() => {
    const getCols = async () => {
      const path = '/salesordermanagement/orders';
      getTemplates({ path });
    };
    getCols();
    const getAllSalesOrder = async () => {
      getSalesOrder();
    };
    getAllSalesOrder();
  }, [getTemplates, getSalesOrder]);

  const setBusinessDetails = useCallback(() => {
    const business = data;

    const address = business?.address?.find(
      (i) => i._id === input?.selectedDetails?.address
    );
    const contact = business?.contactNumber?.find(
      (i) => i._id === input?.selectedDetails?.contact
    );
    const email = business?.emailAddress?.find(
      (i) => i._id === input?.selectedDetails?.email
    );

    setInput((prev) => ({
      ...prev,
      businessDetails: {
        ...prev?.businessDetails,
        companyName: business?.name,
        address: address,
        contact: contact?.number,
        gstNumber: business?.gstNumber,
        email: email?.mail,
      },
    }));
  }, [
    input?.selectedDetails?.address,
    input?.selectedDetails?.contact,
    input?.selectedDetails?.email,
    data,
  ]);

  useEffect(
    () => {
      setBusinessDetails();
    },
    // eslint-disable-next-line
    [
      input?.selectedDetails?.address,
      input?.selectedDetails?.contact,
      input?.selectedDetails?.email,
      data?.name,
      data?.address,
      data?.contactNumber,
      data?.emailAddress,
      data?.gstNumber,
    ]
  );

  useEffect(() => {
    if (props?.isEdit || props?.isCopy) {
      setShowIgst(props?.showIgst || false);
    }
  }, [props]);
  useEffect(() => {
    if (props?.isEdit || props?.isCopy) {
      const allTemplates = props?.allProductTemplates;
      if (allTemplates && allTemplates.length) {
        const productTemplates = allTemplates.filter((temp) =>
          props?.products?.some((product) => product?.value === temp.productId)
        );
        setAllProductTemplates(productTemplates);
      }
      const productsToSet = props?.products?.map((el) => {
        const matchedTemplate = allTemplates?.find(
          (temp) => temp?.productId === el?.value
        );
        return {
          ...(matchedTemplate || []),
          ...el,
        };
      });
      setSelectedProducts(productsToSet);
      setInput((prev) => ({
        ...prev,
        charges: props?.charges,
        productTableFormat: props?.productTableFormat,
        productChargesFromFormat: props?.productChargesFromFormat,
        productDetailsFromFormat: props?.productDetailsFromFormat,
        productTableColumnHideStatus: props?.productTableColumnHideStatus,
        productTableChargesHideStatus: props?.productTableChargesHideStatus,
      }));
    }
  }, [props]);

  const [Searchparams] = useSearchParams();

  useEffect(() => {
    const setIdFormatFunc = () => {
      if (allSalesOrder?.length === 0) {
        if (templatesData) {
          const defaultTemplate = templatesData?.find((template) =>
            template.name.startsWith('Default')
          );
          setAdditionalFields(defaultTemplate);
          setSelectedTemplate(defaultTemplate);
        }
      } else {
        const templateParamsId =
          Searchparams.get('templateId') === 'undefined'
            ? null
            : Searchparams.get('templateId');
        if (allSalesOrder) {
          const lastEntry = allSalesOrder[allSalesOrder?.length - 1];
          const templateToUse = templatesData?.find((template) => {
            return (
              template?._id ===
              (templateParamsId
                ? templateParamsId
                : lastEntry?.additionalFields?._id)
            );
          });
          setSelectedTemplate(templateToUse);
          setAdditionalFields(templateToUse);
        }
      }
    };
    if (props?.edit && !props?.isCopy) {
      const templateToUse = templatesData?.find((template) => {
        return template?._id === props?.additionalFields?._id;
      });
      if (templateToUse) {
        setSelectedTemplate(templateToUse);
        setAdditionalFields(props?.additionalFields);
      }
      return;
    } else if (props?.isCopy) {
      setIdFormatFunc();
    } else {
      setIdFormatFunc();
    }
  }, [
    Searchparams,
    allSalesOrder,
    defaults?.defaultParam?.prefixIds?.salesOrderId,
    props?.additionalFields,
    props?.edit,
    templatesData,
    props?.isCopy,
  ]);

  useEffect(() => {
    if (allAdditionalCharges) {
      const charges = allAdditionalCharges.filter(
        (charge) => charge.pageSlug === '/salesordermanagement/orders'
      );

      if (charges.length > 0) {
        setAdditionalCharges(charges[0]);
        const newCharges = charges[0]?.additionalCharges?.reduce(
          (acc, charge) => {
            acc[charge.name] = 0;
            return acc;
          },
          {}
        );

        // Update state
        if (newCharges) {
          setInput((prev) => ({
            ...prev,
            charges: {
              ...prev.charges,
              ...newCharges,
            },
          }));
        }
      }
    }
  }, [allAdditionalCharges]);

  useEffect(() => {
    if (!dropdowns) return;
    const PaymentTerm = dropdowns?.dropdowns?.find((dropdown) => {
      return dropdown?.name === 'Payment Term';
    });
    setPaymentTermOptions(PaymentTerm?.values);
  }, [dropdowns]);

  useEffect(() => {
    if (!dropdowns) return;
    const hsn = dropdowns?.dropdowns?.find((dropdown) => {
      return dropdown?.name === 'Hsn Code';
    });
    setHsnOptions(hsn?.values);
  }, [dropdowns]);
  useEffect(() => {
    if (props?.edit || props?.isCopy) {
      setAdditionalFieldsHideStatus(props?.additionalFieldsHideStatus || {});
    } else if (latestSo?.additionalFieldsHideStatus) {
      setAdditionalFieldsHideStatus(latestSo?.additionalFieldsHideStatus || {});
    }
  }, [
    props?.edit,
    props?.isCopy,
    latestSo?.additionalFieldsHideStatus,
    props?.additionalFieldsHideStatus,
  ]);

  useEffect(() => {
    (async () => {
      if (searchParams.get('orderId')) {
        // ANCHOR
        let orders = await getAllOrders();
        let currentOrder = orders?.data?.filter(
          (elem) => elem?._id === searchParams.get('orderId')
        )?.[0];
        let steps = currentOrder?.steps;
        let quotation = steps?.find((elem) => elem?.stepPage === 'Quotation');
        setSelectedTermAndCondition(quotation?.data?.termsAndConditions);
        setPdf(quotation?.data?.attachments);
        setInput((prev) => {
          return {
            ...prev,
            quotationID: quotation?.data?.quoteID,
            termsAndConditions: quotation?.data?.termsAndConditions,
            attachments: quotation?.data?.attachments,
          };
        });
        setItems((prev) => {
          const updated = [...prev];
          for (let i = 0; i < quotation?.data?.productDetails.length; i++) {
            updated[i] = {
              ...updated[i],
              hsn: quotation?.data?.productDetails?.[i]?.HsnSacCode,
              details: quotation?.data?.productDetails?.[i]?.productName,
              attachments: quotation?.data?.productDetails?.[i]?.attachments,
              UOM: quotation?.data?.productDetails?.[i]?.uom,
              quantity: quotation?.data?.productDetails?.[i]?.quantity,
              rate: quotation?.data?.productDetails?.[i]?.rate,
              discount: quotation?.data?.productDetails?.[i]?.discount,
              amount: quotation?.data?.productDetails?.[i]?.amount,
              sgst: quotation?.data?.productDetails?.[i]?.sgst,
              cgst: quotation?.data?.productDetails?.[i]?.cgst,
              igst: quotation?.data?.productDetails?.[i]?.igst,
              remarks: quotation?.data?.productDetails?.[i]?.remarks,
              customColumns:
                quotation?.data?.productDetails?.[i]?.customColumns,
            };
          }
          return updated;
        });
      }
    })();
  }, [searchParams.get('orderId')]); //eslint-disable-line

  useEffect(() => {
    const bankDetails = data?.bankDetails?.find(
      (i) => i._id === input?.selectedDetails?.bank
    );

    setInput((prev) => {
      // If isEdit is false and latestSo is present, use the latestSo data
      if (!props.isEdit && latestSo) {
        const {
          productColumnHideStatus,
          customColumnsHideStatus,
          bankDetailsHideStatus,
          termsAndConditionsHide,
        } = latestSo;

        return {
          ...prev,
          bankDetails: {
            accountHolder: bankDetails?.accountHolder || '',
            accountNumber: bankDetails?.accountNumber,
            bankName: bankDetails?.bankName,
            ifsc: bankDetails?.ifsc,
            bankAddress: bankDetails?.bankAddress,
            upiId: bankDetails?.upiId || '',
          },
          customColumnsHideStatus: customColumnsHideStatus || {},
          productColumnHideStatus: productColumnHideStatus || {},
          termsAndConditionsHide: termsAndConditionsHide || {},
          bankDetailsHideStatus: bankDetailsHideStatus || false, // It is a Boolean value
        };
      } else {
        // If isEdit is true, use the props data
        return {
          ...prev,
          bankDetails: props.bankDetails || {},
          customColumnsHideStatus: props?.customColumnsHideStatus || {},
          productColumnHideStatus: props?.productColumnHideStatus || {},
          termsAndConditionsHide: props?.termsAndConditionsHide || {},
          bankDetailsHideStatus: props?.bankDetailsHideStatus || false, // It is a Boolean value
        };
      }
    });

    /*eslint-disable-next-line*/
  }, [data?.bankDetails, input?.selectedDetails?.bank, latestSo, props.isEdit]);

  useEffect(() => {
    if (!props.edit) {
      // console.log(additionalCharges);
      const allProductTemplates = selectedQuotation?.allProductTemplates;
      const products = selectedQuotation?.productDetails
        ? selectedQuotation.productDetails
        : [];
      if (allProductTemplates && allProductTemplates.length) {
        const productTemplates = allProductTemplates.filter((temp) =>
          products.some((product) => product?._id === temp.productId)
        );
        setAllProductTemplates(productTemplates);
      }
      setItems(() => [
        ...products.map((el) => {
          const matchedTemplate = allProductTemplates?.find(
            (temp) => temp?.productId === el?._id
          );
          return {
            ...(matchedTemplate || []),
            details: el?.productName,
            attachments: el?.attachments,
            UOM: el?.uom,
            cgst: parseInt(el?.cgst),
            sgst: parseInt(el?.sgst),
            igst: parseInt(el?.igst),
            quantity: el?.quantity,
            rate: el?.rate,
            amount: el?.amount,
            discount: el?.discount,
            totalAmount: el?.totalAmount,
            hsn: el?.HsnSacCode,
            value: el?._id,
            customColumns: el?.customColumns,
          };
        }),
      ]);

      setSelectedTermAndCondition(
        selectedQuotation?.termsAndConditions?.map((tc) => ({
          label: tc.terms,
          value: { description: tc?.description, terms: tc?.terms, page: [] },
        })) || []
      );

      const matchingCharges = {};
      const quotationCharges = selectedQuotation?.charges || {};
      additionalCharges?.additionalCharges?.forEach((charge) => {
        const found =
          quotationCharges?.[charge?.name] &&
          quotationCharges?.[charge?.name]?.type === charge?.type;
        if (found) {
          matchingCharges[charge.name] = quotationCharges?.[charge?.name];
        }
      });

      setInput((prev) => ({
        ...prev,
        customer: selectedQuotation?.vendorDetails?.name,
        CustomerData: selectedQuotation?.vendorDetails,
        deliveryAddress: selectedQuotation?.vendorDetails?.address || [],
        billingAddress: selectedQuotation?.vendorDetails?.billingAddress?.length
          ? selectedQuotation?.vendorDetails?.billingAddress
          : selectedQuotation?.vendorDetails?.address || [],
        billingGST: selectedQuotation?.vendorDetails?.gstNumber || [],
        billingName: selectedQuotation?.vendorDetails?.companyName || '',
        billingState: '',
        charges: matchingCharges || {},
        termsAndConditions:
          selectedQuotation?.termsAndConditions?.map((tc) => ({
            label: tc.terms,
            value: { description: tc?.description, terms: tc?.terms, page: [] },
          })) || [],
      }));

      const newlyAddedFiles = selectedQuotation?.attachments?.map((item) => {
        return { name: item.name, type: item.type, data: item.data };
      });

      setPdf(() => {
        return [...(newlyAddedFiles || [])];
      });
    }
  }, [
    selectedQuotation,
    setItemsInput,
    props.edit,
    additionalCharges?.additionalCharges,
  ]);

  useEffect(() => {
    setSelectedQuotation(
      quotationData?.find((el) => el?.quoteID === input?.quotationID)
    );
  }, [input.quotationID, quotationData, setSelectedQuotation]);

  useEffect(() => {
    (async () => {
      let productTotal = 0;
      for (let i = 0; i < selectedProducts?.length; i++) {
        const item = selectedProducts[i];
        let amt =
          parseInt(item?.quantity) *
          parseInt(item?.rate) *
          (1 - (parseInt(item?.discount) || 0) / 100);
        const price = await calculateTotalPrice(
          item?.cgst,
          item?.sgst,
          item?.igst,
          amt
        );
        productTotal += +price?.price;
      }
      for (let i = 0; i < items?.length; i++) {
        const item = items[i];
        const price = await calculateTotalPrice(
          item?.cgst || 0,
          item?.sgst || 0,
          item?.igst || 0,
          item?.amount
        );
        productTotal += +price?.price;
      }

      setItemsInput({
        details: '',
        quantity: '',
        rate: '',
        UOM: '',
        cgst: 0,
        sgst: 0,
        igst: 0,
        amount: 0,
        discount: '',
        totalAmount: 0,
      });

      setSubTotal(productTotal);
    })();
  }, [items, selectedProducts]);

  useEffect(() => {
    const amount = +itemsInput?.quantity * +itemsInput?.rate;
    const total = amount - amount * (+itemsInput?.discount / 100);

    setItemsInput((prevState) => ({
      ...prevState,
      amount: total,
      totalAmount:
        total +
        total * (+itemsInput?.cgst / 100) +
        calculateTaxAmount(total, itemsInput?.sgst) +
        calculateTaxAmount(total, itemsInput.sgst),
    }));
  }, [
    itemsInput?.cgst,
    itemsInput?.discount,
    itemsInput?.quantity,
    itemsInput?.rate,
    itemsInput.sgst,
  ]);

  const inputChangeHandler = (e) => {
    setInput((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const inputChangeHandle = (e) => {
    setInput((prev) => {
      return {
        ...prev,
        bankDetails: { ...prev.bankDetails, [e.target.name]: e.target.value },
      };
    });
  };

  const copyAddress = (e) => {
    if (e.target.checked) {
      setInput((prev) => ({
        ...prev,
        deliveryAddress: prev.billingAddress,
      }));
    } else {
      setInput((prev) => ({
        ...prev,
        deliveryAddress: '',
      }));
    }
  };

  const itemsChangeHandler = (e, index) => {
    if (
      (e?.target?.name === 'quantity' ||
        e?.target?.name === 'rate' ||
        e?.target?.name === 'discount') &&
      e?.target?.value < 0
    ) {
      toast.error(`${e?.target?.name} cannot be negative`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Quantity, Rate and Discount cannot be negative',
      });
      return;
    }
    if (e?.target?.name === 'discount' && e?.target?.value > 100) {
      toast.error(`Discount cannot be greater than 100`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Discount cannot be greater than 100',
      });
      return;
    }

    setItems((prev) =>
      prev.map((item, idx) => {
        if (index === idx) {
          if (e.target.name === 'quantity') {
            const discountedPrice =
              (+e.target.value * (item?.rate || 0) * (item.discount || 0)) /
              100;
            const amount =
              +e.target.value * (item?.rate || 0) - discountedPrice;
            let totalAmount = amount;
            if (!isNaN(item.gst)) {
              totalAmount += (amount * +item?.gst) / 100;
            }
            return {
              ...item,
              [e.target.name]: parseFloat(e.target.value),
              amount: amount,
              totalAmount: totalAmount,
            };
          } else if (e.target.name === 'rate') {
            const discountedAmount =
              (parseFloat(item?.quantity) *
                parseFloat(e.target.value || 0) *
                +item?.discount) /
              100;
            const amount = +item.quantity * +e.target.value - discountedAmount;
            let totalAmount = amount;
            if (!isNaN(item.gst)) {
              totalAmount += (amount * +item?.gst) / 100;
            }
            return {
              ...item,
              [e.target.name]: parseFloat(e.target.value || 0),
              amount: amount,
              totalAmount: totalAmount,
            };
          } else if (e.target.name === 'discount') {
            const total = item?.quantity * item?.rate * (+e.target.value / 100);
            const amount = item?.quantity * item?.rate - total;

            return {
              ...item,
              discount: +e?.target?.value,
              amount: amount,
              totalAmount: amount,
            };
          } else if (e.target.name === 'hsn') {
            if (e.target.value === '+') {
              setShowHsnSacCodeModal(true);
              return item;
            }
            return {
              ...item,
              hsn: e.target.value,
            };
          } else {
            return {
              ...item,
              [e.target.name]: e.target.value,
            };
          }
        } else {
          return item;
        }
      })
    );
  };

  const removeItem = (el) => {
    const filtered = items.filter((item) => item?.details !== el?.details);
    setItems(filtered);
  };

  const linkSalesOrderToWorkorder = async (workorderid, salesOrder) => {
    await linkSalesOrder({
      id: workorderid,
      data: { salesOrder: salesOrder },
    });
  };

  const [updateNcr] = useUpdateNcrMutation();

  const itemsFormulaHandler = (_products) => {
    let transformedItems = [];

    // Combine the formData _products and items into a single array
    const allProducts = [...(_products || []), ...(items || [])];

    for (let item of allProducts) {
      let temp = { ...item };

      if (item?.customColumns) {
        for (let columnInput of columnInputs) {
          if (columnInput?.formula) {
            let val = {};
            let formula = columnInput?.formula?.substring(
              columnInput?.formula?.indexOf('{') + 1,
              columnInput?.formula?.indexOf('}')
            );
            formula = formula.trim();
            formula = formula + ' ';

            while (formula?.indexOf('$') !== -1) {
              let key = formula?.substring(
                formula?.indexOf('$') + 1,
                formula?.indexOf(' ')
              );
              let stringRegex = /^[A-Za-z]+$/;
              if (stringRegex.test(key)) {
                val = {
                  ...val,
                  [key]:
                    item?.[key] || parseFloat(item?.customColumns?.[key]) || 0,
                };
              }
              formula = formula?.substring(formula?.indexOf(' ') + 1);
            }
            const allFields = [
              {
                name: 'cgst',
                percentOf: 'amount',
              },
              {
                name: 'sgst',
                percentOf: 'amount',
              },
              {
                name: 'igst',
                percentOf: 'amount',
              },
              {
                name: 'discount',
                percentOf: 'amount',
              },
            ];
            let res = handleFormula(columnInput, val, allFields);

            // Add or update the customColumns field with the calculated value
            temp.customColumns = {
              ...temp.customColumns,
              [columnInput?.columnName]: res,
            };
          }
        }
      }

      transformedItems.push(temp); // Push the transformed product details into the array
    }
    return transformedItems;
  };
  const submitHandler = async (e, bool) => {
    try {
      setIsSetAsDefault(true);
      if (e) {
        e.preventDefault();
      }

      if (input?.shipping < 0) {
        toast.error(`Shipping cannot be negative`, {
          theme: 'colored',
          position: 'top-right',
          toastId: 'Discount and Shipping cannot be negative',
        });
        return;
      }
      let _products = [];
      for (let item of selectedProducts) {
        let obj = { ...item };
        if (item?.quantity && item?.rate) {
          let amt =
            parseInt(item?.quantity) *
            parseInt(item?.rate) *
            (1 - (parseInt(item?.discount) || 0) / 100);
          const { price } = calculateTotalPrice(
            item?.cgst,
            item?.sgst,
            item?.igst,
            item?.amount
          );
          obj = {
            ...obj,
            totalAmount: price,
            amount: amt,
          };
        }
        _products?.push(obj);
      }
      _products = itemsFormulaHandler(_products);

      // const _products = items.map((item) => {
      //   if (item?.quantity && item?.rate) {
      //     let amt = parseInt(item?.quantity) * parseInt(item?.rate) * (1 - (parseInt(item?.discount) || 0) / 100);
      //     const { price } = calculateTotalPrice(
      //       item?.cgst,
      //       item?.sgst,
      //       item?.igst,
      //       item?.amount
      //     );
      //     return {
      //       ...item,
      //       totalAmount: price,
      //       amount: amt
      //     };
      //   }
      // });
      if (!displayFormat) {
        if (items?.length === 0 && selectedProducts?.length === 0) {
          return toast.error('Please Select Atleast One Product');
        }
        for (let i = 0; i < items?.length; i++) {
          if (!items[i]?.details) {
            return toast.error('Please Enter Product Details');
          }
        }
        for (let i = 0; i < selectedProducts?.length; i++) {
          if (!selectedProducts[i]?.details) {
            return toast.error('Please Enter Product Details');
          }
        }
      }
      const updatedTerms = selectedTermAndCondition?.map((item) => item?.value);
      const hasUndefinedValues = updatedTerms.some(
        (value) => value === undefined
      );
      const billingGST = Array.isArray(input.billingGST)
        ? input.billingGST[0]
        : input.billingGST;
      let data = {
        ...input,
        billingGST,
        charges: {
          ...input?.charges,
          subTotal,
          total:
            subTotal +
              input?.charges?.shipping +
              input?.charges?.packaging +
              input?.charges?.installation +
              input?.charges?.shippingTax +
              input?.charges?.packagingTax +
              input?.charges?.installationTax || 0,
        },
        deliveryAddress:
          typeof input?.deliveryAddress === 'string'
            ? input.deliveryAddress
            : input?.deliveryAddress?.[0],
        billingAddress:
          typeof input?.billingAddress === 'string'
            ? input.billingAddress
            : input?.billingAddress?.[0],
        products: defaults?.defaultParam?.projectDefaults
          ?.showProductFormatTable
          ? formatSalesOrderProducts(tableFormatProducts)
          : _products,
        items: _products?.length > 0 ? [] : items,
        salesOrderDate: new Date().toISOString(),
        files: pdf,
        additionalFields: additionalFields,
        createdBy,
        termsAndConditions: hasUndefinedValues
          ? selectedTermAndCondition
          : updatedTerms,
        ncrId: Searchparams.get('ncrId') ? Searchparams.get('ncrId') : null,
        showIgst,
        subTotal: subtotalFormulaPrice,
        allProductTemplates,
        additionalFieldsHideStatus,
        productTableFormat: displayFormat?._id,
        productDetailsFromFormat: tableFormatProducts,
        productChargesFromFormat: tableFormatsCharges,
        productTableChargesHideStatus: chargesVisibility,
        productTableColumnHideStatus: columnVisibility,
        productTableFormatHideStatus:
          defaults?.defaultParam?.projectDefaults?.showProductFormatTable,
        salesOrderStatus:
          input?.salesOrderStatus === 'rejected'
            ? 'pending'
            : input?.salesOrderStatus,
      };

      if (defaults?.defaultParam?.projectDefaults?.salesVoucherAutoCreate) {
        data = {
          ...data,
          idDataVoucher: idCompData?.dataToReturn,
        };
      }

      const sendObj = {
        data,
        files: pdf,
      };
      let res = {};
      if (props.edit && !props.isCopy) {
        sendObj.id = props._id;
        res = await updateOrder(sendObj);
      } else {
        res = await createOrder(sendObj);
        // if (res?.error == null)
        //   await updateDefaults({ salesOrderId: props?.newId + 1 });
      }

      if (res.data) {
        toast.success(
          props.edit && !props?.isCopy
            ? `Sales Order Successfully Edited`
            : props?.isCopy
              ? `Sales Order Copied SuccessFully`
              : `Sales Order Created Successfully`,
          {
            theme: 'colored',
            position: 'top-right',
            toastId: 'Sales Order Created Successfully',
          }
        );

        for (let i = 0; i < input?.workOrders?.length; i++) {
          await linkSalesOrderToWorkorder(
            input?.workOrders[i]?.value,
            res?.data?._id
          );
        }

        const kanban = searchParams.get('kanban') === 'true';
        const orderId = searchParams.get('orderId');
        const allProducts = _products?.map((product) => product?.details);
        const kanbanFilterObj = {
          customer_name: input?.CustomerData?.name,
          company_name: input?.CustomerData?.company_name,
          product_details: allProducts,
        };

        const navigateParams = {
          department: searchParams.get('department'),
          id: res?.data?._id,
          refType: searchParams.get('refType'),
          page: searchParams.get('page'),
          taskId: searchParams.get('taskId'),
          orderId,
          index: searchParams.get('index'),
          idIndex: additionalFields?.idIndex,
          filterDetails: JSON.stringify(kanbanFilterObj),
        };
        if (!kanban) {
          let obj = {
            objRef: res?.data?._id,
            currentDepartment: 'sales',
            refKey: 'SalesOrder',
            currentPage: 'Sales Order',
            userId: state?.user?._id,
            filterDetails: JSON.stringify(kanbanFilterObj),
          };
          if (props.edit && !props?.isCopy) {
            if (res?.data?.taskId) {
              await updateDepOrder({
                data: {
                  ...obj,
                  id: res?.data?.taskId,
                },
              });
            }
          } else {
            await createDepOrder({
              data: obj,
            });
          }

          if (searchParams.get('ncrId')) {
            const ncrs = props.ncr;
            const ncrToUpdate = ncrs.find(
              (ncr) => ncr._id === searchParams.get('ncrId')
            );
            if (ncrToUpdate) {
              await updateNcr({
                data: {
                  ...ncrToUpdate,
                  checkedOrder: {
                    orderId: res?.data?._id,
                  },
                },
                id: ncrToUpdate._id,
                orderCheck: false,
              });
            }
          }
        }

        if (kanban) {
          let time = new Date();
          dispatch({
            type: 'ADD_CARD',
            payload: {
              data: {
                taskId: searchParams.get('taskId'),
                firstStepId: res?.data?.salesOrderID,
                stepPage: 'Sales Order',
                updatedAt: time?.toDateString(),
                filterDetails: kanbanFilterObj,
              },
              currentColumn: 'Sales Order',
            },
          });
        }

        const filteredParams = Object.fromEntries(
          Object.entries(navigateParams).filter(([_, value]) => value !== null)
        );

        const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;

        if (kanban) {
          navigate(navigateStr);
        } else if (searchParams.get('navigateTo')) {
          navigate(searchParams.get('navigateTo'));
        } else {
          navigate('/salesordermanagement/orders');
          props?.setIsAdd(false);
          props?.setIsEdit(false);
        }

        if (bool) {
          props.modalHandler(false);
        }
        return true;
      } else {
        if (props.edit) {
          toast.error(
            props.edit
              ? `Error in editing Sales Order`
              : `Error in creating Sales Order`,
            {
              theme: 'colored',
              position: 'top-right',
              toastId: 'Error in creating Sales Order',
            }
          );
        }
        return false;
      }
    } catch (error) {
      console.log(error); // eslint-disable-line
    }
  };

  const shareAndSubmitHandler = async (e) => {
    e.preventDefault();
    if (!input.bankDetails) {
      toast.error(`Please Enter Bank Details`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'All fields are required',
      });
      return false;
    }
    if (!input.termsAndConditions) {
      toast.error(`Please Enter Terms and Conditions`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'All fields are required',
      });
      return false;
    }
  };
  return (
    <>
      {/* <div className="w-fit mx-auto mt-5 mb-3 flex justify-center overflow-x-scroll no-scrollbar">
        <BreadCrumbs pages={pages} step={formStep - 1} setStep={setFormStep} />
      </div> */}

      {showHsnSacModal && (
        <HsnSacModal
          HsnOptions={HsnOptions}
          setShowModal={setShowHsnSacCodeModal}
          dropdowns={dropdowns}
        />
      )}
      <div className="bg-white pt-4">
        <div>
          <Button
            textColor="text-gray-600"
            className="bg-white border-2 text-gray-600 mb-6 ml-3"
            onClick={() => {
              props?.setIsAdd(false);
              props?.setIsEdit(false);
            }}
          >
            Back
          </Button>
          <div className="px-4 !w-[500px]">
            <Header
              title={props?.isEdit ? 'Edit Sales Order' : 'Create Sales Order'}
              description=""
              infoTitle="Welcome to Create Sales Order Page"
              infoDesc=""
              paras={[
                'Effortlessly manage accounts with our Proforma Invoice page. Simplify product data entry and streamline procurement processes. Easily input item details, quantities, and remarks for accurate records.',
              ]}
            />
          </div>
          <div className="flex flex-col justify-between h-full">
            <>
              <OrderDetails
                isMobile={props.isMobile}
                isTablet={props.isTablet}
                input={input}
                setInput={setInput}
                inputChangeHandler={inputChangeHandler}
                isEdit={props.edit}
                isCopy={props.isCopy}
                setItemsInput={setItems}
                quotationData={props.edit ? filtered : quotationData}
                selectedQuotation={selectedQuotation}
                setSelectedQuotation={setSelectedQuotation}
                setIsDefaultDeliveryAddress={setIsDefaultDeliveryAddress}
                isDefaultDeliveryAddress={isDefaultDeliveryAddress}
                setIsDefaultBillingAddress={setIsDefaultBillingAddress}
                isDefaultBillingAddress={isDefaultBillingAddress}
                copyAddress={copyAddress}
                paymentTermOptions={PaymentTermOptions}
                additionalFields={additionalFields}
                setAdditionalFields={setAdditionalFields}
                setSelectedTemplate={setSelectedTemplate}
                selectedTemplate={selectedTemplate}
                templatesData={templatesData}
                orders={props?.orders}
                Searchparams={Searchparams}
                defaults={defaults}
                addressSelector={addressSelector}
                contactSelector={contactSelector}
                emailSelector={emailSelector}
                profileSelector={profileSelector}
                salesOrderID={props?.salesOrderID}
                voucherID={props?.voucherID}
                additionalFieldsHideStatus={additionalFieldsHideStatus}
                setAdditionalFieldsHideStatus={setAdditionalFieldsHideStatus}
                IdGenCompVoucher={IdGenComp}
                idCompDataVoucher={idCompData}
              />
              {defaults?.defaultParam?.projectDefaults
                ?.showProductFormatTable ? (
                <ProductFormatManager
                  input={tableFormatProducts}
                  setInput={setTableFormatProducts}
                  charges={tableFormatsCharges}
                  setCharges={setTableFormatsCharges}
                  columnVisibility={columnVisibility}
                  setColumnVisibility={setColumnVisibility}
                  chargesVisibility={chargesVisibility}
                  setChargesVisibility={setChargesVisibility}
                  displayFormat={displayFormat}
                  setDisplayFormat={setDisplayFormat}
                  isEdit={props?.isEdit}
                  isCopy={props?.isCopy}
                  data={selectedQuotation ? selectedQuotation : input}
                  latestSo={latestSo}
                />
              ) : (
                <>
                  <ProductTable
                    isMobile={props.isMobile}
                    isTablet={props.isTablet}
                    items={items}
                    setPdf={setPdf}
                    removeItemHandler={removeItem}
                    itemsInput={itemsInput}
                    itemsChangeHandler={itemsChangeHandler}
                    setItems={setItems}
                    selectedProducts={selectedProducts}
                    setSelectedProducts={setSelectedProducts}
                    HsnOptions={HsnOptions}
                    showIgst={showIgst}
                    setColumnInputs={setColumnInputs}
                    setShowIgst={setShowIgst}
                    columnInputs={columnInputs}
                    input={input}
                    setInput={setInput}
                    setOpenAddUomModal={setOpenAddUomModal}
                    openAddUomModal={openAddUomModal}
                    hidePoTable={hidePoTable}
                    setHidePoTable={setHidePoTable}
                    pageSlug={pageSlug}
                    allProductTemplates={allProductTemplates}
                    setAllProductTemplates={setAllProductTemplates}
                    isEdit={props?.isEdit}
                  />
                  <Charges
                    isMobile={props.isMobile}
                    isTablet={props.isTablet}
                    formData={{ ...input, productDetails: items }}
                    setFormData={setInput}
                    totalPrice={subTotal}
                    additionalFields={additionalFields}
                    setAdditionalFields={setAdditionalFields}
                    additionalCharges={additionalCharges}
                    selectedProducts={selectedProducts}
                    pageSlug={`/salesordermanagement/orders`}
                    setSubtotalFormulaPrice={setSubtotalFormulaPrice}
                    hidePoTable={hidePoTable}
                    showIgst={showIgst}
                  />
                </>
              )}

              <Attachments
                isMobile={props.isMobile}
                isTablet={props.isTablet}
                pdf={pdf}
                setPdf={setPdf}
                input={input}
                setInput={setInput}
                inputChangeHandle={inputChangeHandle}
                inputChangeHandler={inputChangeHandler}
                edit={props.edit}
                shareAndSubmitHandler={shareAndSubmitHandler}
                setIsDefaultTermsAndConditions={setIsDefaultTermsAndConditions}
                isDefaultTermsAndConditions={isDefaultTermsAndConditions}
                setIsDefaultBankDetails={setIsDefaultBankDetails}
                isDefaultBankDetails={isDefaultBankDetails}
                selectedTermAndCondition={selectedTermAndCondition}
                setSelectedTermAndCondition={setSelectedTermAndCondition}
                bankDetailsSelector={bankDetailsSelector}
                isSetAsDefault={isSetAsDefault}
                page={['SO']}
              />
            </>
          </div>
          <div className="flex items-center justify-end p-3">
            {props?.isAdd || props?.isCopy ? (
              <Button
                isLoading={isCreateSalesOrderLoadig}
                onClick={submitHandler}
              >
                Save
              </Button>
            ) : (
              <Button
                isLoading={isUpdateSalesOrderLoading}
                onClick={submitHandler}
              >
                Save
              </Button>
            )}
          </div>
        </div>
      </div>
      {/* <Modal
        onSubmit={(e) => {
          submitHandler(e, true);
        }}
        pages={['Order', 'Products', 'Charges', 'Details']}
        onNextClick={({ step, setStep }) => nextHandler(step + 1, setStep)}
        onBackClick={({ step, setStep }) => backHandler(step + 1, setStep)}
        svg={<AnalyticsIcon className="h-8 w-8" />}
        title={
          props.edit
            ? `Edit ${props.salesOrderID} order`
            : 'Create New Sales Order'
        }
        description="Used to create and edit sales orders."
        onCloseModal={async () => {
          const confirmation = await customConfirm(
            'Are you sure you want to go back, all the changes will be lost?',
            'delete'
          );
          if (confirmation) {
            props.modalHandler();
          }
        }}
      >
        {({ step: tempStep }) => {
          const step = tempStep + 1;
          return (
            
          );
        }}
      </Modal> */}
    </>
  );
};

export default CreateModal;
