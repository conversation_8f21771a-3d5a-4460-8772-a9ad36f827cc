import { AnimatePresence, motion } from 'framer-motion';
import { useRef, useState } from 'react';

const UploadButton = ({
  onChange,
  accept,
  multiple,
  disabled = false,
  width = '',
  maxSizeMB = 4,
  size = 'lg',
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [sizeError, setSizeError] = useState(false);

  const inputRef = useRef();

  // Check if file size is within limits
  const validateFileSize = (files) => {
    if (!files) return false;

    const maxSizeBytes = maxSizeMB * 1024 * 1024; // Convert MB to bytes

    if (files instanceof FileList) {
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > maxSizeBytes) {
          return false;
        }
      }
      return true;
    } else {
      return files.size <= maxSizeBytes;
    }
  };

  // handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // triggers when file is dropped
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    setSizeError(false);

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;

    if (!validateFileSize(files)) {
      setSizeError(true);
      return;
    }

    if (!multiple) {
      onChange(files[0]);
    } else {
      onChange(files);
    }
  };

  // triggers when file is selected with click
  const handleChange = (e) => {
    e.preventDefault();
    setSizeError(false);

    const files = e.target.files;
    if (!files || files.length === 0) return;

    if (!validateFileSize(files)) {
      setSizeError(true);
      e.target.value = '';
      return;
    }

    if (!multiple) {
      onChange(files[0]);
    } else {
      onChange(files);
    }
    e.target.value = '';
  };

  // triggers the input when the button is clicked
  const onClick = () => {
    inputRef.current.click();
  };

  return (
    <div className={`relative ${width ? `w-[${width}]` : 'w-fit'}`}>
      <div
        onDragEnter={handleDrag}
        onClick={onClick}
        className={`
          relative overflow-hidden rounded-lg cursor-pointer
          transition-all duration-300 ease-in-out
          ${dragActive ? 'bg-blue-50' : 'bg-white'}
          ${sizeError ? 'border-red-400' : dragActive ? 'border-blue-400' : `${size !== 'sm' ? 'border-gray-100' : 'border-gray-300'}`}
          ${size !== 'sm' ? 'border-2' : 'border-[1px]'} hover:border-blue-200 ${size !== 'sm' ? 'hover:shadow-md' : ''} ${disabled ? 'cursor-not-allowed !bg-slate-100/50 hover:!shadow-none !border-gray-100' : ''}
        `}
      >
        <input
          ref={inputRef}
          type="file"
          disabled={disabled}
          className="hidden w-0 h-0 opacity-0 absolute pointer-events-none"
          accept={accept}
          onChange={handleChange}
          multiple={multiple}
        />

        <div
          className={`flex items-center ${size !== 'sm' ? 'p-4' : 'py-[2px] px-[4px]'} space-x-4`}
        >
          <div
            className={`p-2 rounded-full ${sizeError ? 'bg-red-50' : 'bg-blue-50'}`}
          >
            <motion.svg
              animate={{ rotate: dragActive ? 180 : 0 }}
              transition={{ duration: 0.3 }}
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className={`${size !== 'sm' ? 'w-6 h-6' : 'w-3 h-3'} ${sizeError ? 'text-red-500' : 'text-blue-500'}`}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
              />
            </motion.svg>
          </div>

          <div className="flex-1">
            <h3
              className={`text-sm font-medium ${sizeError ? 'text-red-700' : 'text-gray-700'}`}
            >
              {sizeError
                ? 'File too large'
                : dragActive
                  ? `Drop files here ${size === 'sm' ? `(Max:${maxSizeMB}MB)` : ''}`
                  : `Upload ${multiple ? 'files' : 'file'} ${size === 'sm' ? `(Max:${maxSizeMB}MB)` : ''}`}
            </h3>
            {size === 'lg' && (
              <p
                className={`text-xs mt-1 ${sizeError ? 'text-red-500' : 'text-gray-500'}`}
              >
                {sizeError
                  ? `Files must be smaller than ${maxSizeMB}MB`
                  : multiple
                    ? `Drop multiple files or click to browse (max ${maxSizeMB}MB each)`
                    : `Drop a file or click to browse (max ${maxSizeMB}MB)`}
              </p>
            )}
          </div>
        </div>

        <AnimatePresence>
          {dragActive && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className={`absolute inset-0 ${sizeError ? 'bg-red-400/10' : 'bg-blue-400/10'} pointer-events-none`}
            />
          )}
        </AnimatePresence>
      </div>

      {dragActive && (
        <div
          className="absolute inset-0"
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        />
      )}
    </div>
  );
};

export default UploadButton;
