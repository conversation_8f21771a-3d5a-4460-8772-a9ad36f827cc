import { ArrowLeftOutlined, DeleteOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useContext, useEffect, useRef, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  dateOptions,
  generateDateString,
  mobileWidth,
  renderFieldsBasedOnType,
  tabletWidth,
} from '../../helperFunction.js';
import usePrefixIds from '../../hooks/usePrefixIds.js';
import { useLazyGetAllcustomerQuery } from '../../slices/customerDataSlice';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice.js';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice.js';
import {
  useLazyGetAllFormQuery,
  useLazyGetResponseQuery,
} from '../../slices/online/createOrderFormApiSlice.js';
import { useCreateOrderMutation } from '../../slices/orderApiSlice.js';
import { useGetProductsQuery } from '../../slices/productApiSlice.js';
import {
  useAddSalesInquiryMutation,
  useGetSalesInquiriesQuery,
} from '../../slices/salesInquiryDashboardApiSlice.js';
import { useUpdateSalesInquiryFormResponseMutation } from '../../slices/salesInquiryFormResponseApiSlice.js';
import { Store } from '../../store/Store.js';
import CaptureCamera from '../global/components/CaptureCamera.jsx';
import CustomerModalFormData from '../global/components/CustomerModalFormData.jsx';
import Input from '../global/components/Input.jsx';
import RightSidebar from '../global/components/RightSidebar';
import SelectV2 from '../global/components/SelectV2.jsx';
import Spinner from '../global/components/Spinner.jsx';
import Table from '../global/components/Table.jsx';
import Textarea from '../global/components/Textarea.jsx';
import CustomToolTip from '../global/CustomToolTip.jsx';
import UploadButton from '../UploadButton.jsx';
import { Label } from '../v2/index.js';
import EditButton from '../v3/global/components/EditButton';
import MediaMetaDataCard from '../v3/global/components/MediaMetaDataCard';
import MediaModal from '../v3/global/components/MediaModal';
import AddUomModal from '../v3/InventoryMasters/AddUomModal.jsx';

export default function SalesInquiryModal({
  isAdd,
  isEdit,
  setIsAdd,
  customerID,
  copyData,
  isCopy,
  setIsCopy,
}) {
  const [searchParams] = useSearchParams();
  const [OpenCamera, setOpenCamera] = useState(false);
  const [capturedImage, setCapturedImage] = useState([]);
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  let navigate = useNavigate();
  const { defaults, state, dispatch } = useContext(Store);
  const [followUpDate, setFollowUpDate] = useState('');
  const [createDepOrder] = useCreateOrderMutation();
  const [pdf, setpdf] = useState([]);
  const [companyName, setCompanyName] = useState('');
  const [ShowEditMetaData, setShowEditMetaData] = useState(false);
  const [ShowMetaDataSidebar, setShowMetaDataSidebar] = useState(false);
  const [addSalesInquiry, { isLoading: isAddSalesInquiryLoading }] =
    useAddSalesInquiryMutation();
  const createdBy = state?.user?.name;

  const [forms, setForms] = useState([]);
  const [isAdded, setIsAdded] = useState(false);
  const [response, setResponse] = useState([]);
  const [CustomerData, setCustomerData] = useState({
    name: '',
    company_name: '',
    unique_id: '',
    phone_no: '',
    address: '',
    gstNumber: '',
  });

  const [selectedForm, setSelectedForm] = useState('');
  const [selectedResponse, setSelectedResponse] = useState('');
  const [remark, setRemark] = useState('');
  const [products, setProducts] = useState([]);
  const [showForms, setShowForms] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [onSalesInquiryCreate] = useUpdateSalesInquiryFormResponseMutation();
  const [getCustomers] = useLazyGetAllcustomerQuery();
  const location = useLocation();
  const pathRef = useRef(location.pathname);
  const [additionalFields, setAdditionalFields] = useState(null);
  const [inquiryName, setInquiryName] = useState('');
  const [industryType, setIndustryType] = useState('');
  const [ShowCustomerModal, setShowCustomerModal] = useState(false);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const { data: SalesInquiryData } = useGetSalesInquiriesQuery();
  const [minDate, setMinDate] = useState('');

  const { IdGenComp, idCompData, taskId } = usePrefixIds({
    idFor: 'salesInquiryId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
  });
  const { dataToReturn: idData } = idCompData;
  const { data: productsRes } = useGetProductsQuery();
  const inventoryProducts = productsRes?.products || [];
  const [Searchparams] = useSearchParams();
  const [openAddUomModal, setOpenAddUomModal] = useState(false);
  const { data: dropdownsData } = useGetDropdownsQuery();
  const handleAddProduct = () => {
    setProducts([
      ...products,
      {
        productName: '',
        quantity: '',
        uom: '',
      },
    ]);
  };

  const handleRemoveProduct = (index) => {
    const updatedProducts = products.filter((_, i) => i !== index);
    setProducts(updatedProducts);
  };
  useEffect(() => {
    if (SalesInquiryData?.length === 0 && SalesInquiryData) {
      const defaultTemplate = templatesData?.find((template) =>
        template.name.startsWith('Default')
      );
      setAdditionalFields(defaultTemplate);
      setSelectedTemplate(defaultTemplate);
    } else if (SalesInquiryData?.length > 0 && SalesInquiryData) {
      const lastEntry = SalesInquiryData[SalesInquiryData?.length - 1];
      const previouslyUsedTemplate = templatesData?.find(
        (item) => item.name === lastEntry?.additionalFields?.name
      );
      if (SalesInquiryData && previouslyUsedTemplate) {
        const templateParamsId =
          Searchparams.get('templateId') === 'undefined'
            ? null
            : Searchparams.get('templateId');
        const lastEntry = SalesInquiryData[SalesInquiryData?.length - 1];
        const valueToAdd = templatesData?.find((template) => {
          return (
            template?._id ===
            (templateParamsId
              ? templateParamsId
              : lastEntry?.additionalFields?._id)
          );
        });
        setSelectedTemplate(valueToAdd);
        setAdditionalFields(valueToAdd);
      }
    }
  }, [
    templatesData,
    SalesInquiryData,
    defaults?.defaultParam?.prefixIds?.salesInquiryId,
    Searchparams,
  ]);

  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    setMinDate(today);
  }, []);

  useEffect(() => {
    if (isCopy) {
      const filteredArr = Object.keys(copyData)?.filter(
        (key) =>
          key !== '_id' &&
          key !== 'createdAt' &&
          key !== 'updatedAt' &&
          key !== 'lastUsed' &&
          key !== '__v'
      );
      const updatedRowData = filteredArr.reduce((acc, key) => {
        acc[key] = copyData[key];
        return acc;
      }, {});

      setInquiryName(updatedRowData?.inquiryName);
      setIndustryType(updatedRowData?.industryType);
      setSelectedCustomer(updatedRowData?.CustomerData?._id);
      setRemark(updatedRowData?.remark);
      setAdditionalFields(updatedRowData?.additionalFields);
      setSelectedTemplate(updatedRowData?.additionalFields);
      setCompanyName(updatedRowData?.companyName);
      setCustomerData((prev) => ({
        ...prev,
        address: updatedRowData?.CustomerData?.address,
        gstNumber: updatedRowData?.CustomerData?.gstNumber,
        name: updatedRowData?.CustomerData?.name,
        phone_no: updatedRowData?.CustomerData?.phone_no,
        unique_id: updatedRowData?.CustomerData?.unique_id,
        company_name: updatedRowData?.CustomerData?.company_name,
      }));
      setpdf(updatedRowData?.files);
      setFollowUpDate(updatedRowData?.followUpDate);
    }
  }, [copyData, isCopy]);

  useEffect(() => {
    const getCols = async () => {
      const path = pathRef.current;
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    const getCustomersData = async () => {
      setIsLoading(true);
      try {
        const customersData = await getCustomers().unwrap();
        setCustomers(customersData?.customers);
      } catch (error) {
        return;
      } finally {
        setIsLoading(false);
      }
    };
    getCustomersData();
  }, [getCustomers]);

  useEffect(() => {
    if (customers.length > 0 && !isCopy && isEdit) {
      const customerData = customers?.find((item) => item?._id === customerID);
      setSelectedCustomer(customerID);
      setCustomerData(customerData);
    }
  }, [customerID, customers, isCopy, isEdit]);

  useEffect(() => {
    pathRef.current = location.pathname;
  }, [location.pathname]);

  async function handleSubmitModal() {
    // console.log(newDefault);
    // return;
    if (inquiryName === '') {
      toast.error('Inquiry Name is required', { toastId: 'inquiry' });
      return;
    }
    const dateToSend =
      followUpDate instanceof Date
        ? followUpDate.toISOString().slice(0, 10)
        : followUpDate;
    const res = await addSalesInquiry({
      companyName,
      inquiryName,
      industryType,
      // remarks,
      customerName: CustomerData?.name,
      remark,
      products,
      files: pdf,
      CustomerData,
      createdBy,
      additionalFields,
      followUpDate: dateToSend,
      idData,
    }).unwrap();
    if (res?.salesInquiry?.inquiryName) {
      toast.success('Sales Inquiry Created');
    }
    if (res) {
      const kanban = searchParams.get('kanban') === 'true';
      const orderId = searchParams.get('orderId');
      const navigateParams = {
        department: searchParams.get('department'),
        id: res?.salesInquiry?._id,
        refType: searchParams.get('refType'),
        page: searchParams.get('page'),
        taskId: searchParams.get('taskId'),
        orderId,
        index: searchParams.get('index'),
        idIndex: additionalFields?.idIndex,
      };

      if (!kanban) {
        let obj = {
          objRef: res?.salesInquiry?._id,
          currentDepartment: 'sales',
          refKey: 'SalesInquiryDashboard',
          currentPage: 'Sales Inquiry',
          userId: state?.user?._id,
        };
        await createDepOrder({
          data: obj,
        });
      }

      if (kanban && !orderId) {
        let time = new Date();
        dispatch({
          type: 'ADD_CARD',
          payload: {
            data: {
              taskId: searchParams.get('taskID'),
              firstStepId: res?.salesInquiry?.salesInquiryId,
              stepPage: 'Sales Inquiry',
              updatedAt: time?.toDateString(),
              obj: res?.salesInquiry,
              refKey: searchParams.get('refType'),
              currentDepartment: searchParams.get('department'),
            },
            currentColumn: 'Sales Inquiry',
          },
        });
      }

      const filteredParams = Object.fromEntries(
        Object.entries(navigateParams).filter(([_, value]) => value !== null)
      );

      const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;

      if (kanban) {
        navigate(navigateStr);
      }
      setIsAdd(false);
      setIsCopy(false);
      if (isCopy) {
        toast.success(`SI Copied Successfully`);
      }
    }

    if (selectedResponse) {
      onSalesInquiryCreate({
        id: selectedResponse?._id,
      });
    }
  }
  /**
   * The function handleCloseModal sets the state of modalOpen to false.
   */

  /**
   * The function `changeHandler` takes an event object as input, iterates over its properties, reads
   * the data of each file using `FileReader`, and then adds the file data to an array called `pdf`
   * using the `setpdf` function.
   * @returns The function does not have a return statement.
   */
  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fname,
          type: ftype,
          data: url,
        };
        setpdf((prev) => [...prev, data]);
      });
    }
  };

  const PdfHandler = (files) => {
    for (let i = 0; i < files?.length; i++) {
      let file = files[i];

      let fname = file.name;
      let ftype = file?.file_type;
      let url = file?.data;
      if (!ftype || !url) return;
      let data = {
        name: fname,
        type: ftype,
        data: url,
      };
      setpdf((prev) => [...prev, data]);
    }
  };

  /**
   * The function `removePdf` removes a PDF file from a list based on its name.
   */
  const removePdf = (name) => {
    const filtered = pdf.filter(
      (itemm) => (itemm.fname ?? itemm.name) !== name
    );
    setpdf(filtered);
  };

  const [getAllForm] = useLazyGetAllFormQuery();

  const [getResponse] = useLazyGetResponseQuery();

  useEffect(
    function () {
      const getForms = async () => {
        try {
          const data = await getAllForm({}, false).unwrap();
          if (data?.forms) {
            setForms(data?.forms);
          }
        } catch (error) {
          toast.error(error?.message);
        }
      };
      getForms();
    },
    [getAllForm]
  );

  useEffect(
    function () {
      const getresponse = async () => {
        try {
          const data = await getResponse({}, false).unwrap();
          if (data?.response) {
            setResponse(data?.response);
            // console.log(data?.response);
          }
        } catch (error) {
          toast.error(error?.message);
        }
      };
      getresponse();
    },
    [getResponse]
  );

  // console.log(response);
  // console.log(copyData)

  function handleAddProducts(formProducts) {
    let addProducts = [];
    for (let i = 0; i < formProducts?.length; i++) {
      let formProduct = formProducts[i];

      if (formProduct.product_name !== '') {
        let uom = inventoryProducts.find(
          (item) => item._id === formProduct.product_name
        )?.uom;
        let name = inventoryProducts.find(
          (item) => item._id === formProduct.product_name
        )?.name;

        addProducts.push({
          productName: name,
          quantity: formProduct.quantity,
          uom,
        });
      }
    }
    setProducts([...products, ...addProducts]);
  }

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setTemplateDropDownModal(true);
      setDropdownIdx(idx);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => {
        return {
          ...prev,
          templateData: updatedAdditionalFields,
        };
      });
    }
  };
  const [dropdownIdx, setDropdownIdx] = useState(null);

  const handleSelectCustomer = async (customer) => {
    if (customer === 'add new') {
      setSelectedCustomer(null);
      setShowCustomerModal(true);
      setCustomerData({
        name: '',
        company_name: '',
        unique_id: [''],
        phone_no: [''],
        address: [''],
        gstNumber: [''],
      });
    } else {
      setSelectedCustomer(customer);
      const customerData = customers?.find((item) => item?._id === customer);
      setCustomerData(customerData);
      setCompanyName(customerData?.company_name);
    }
  };

  const [ColumnsValue, setColumnsValue] = useState({});

  const handleSetDate = (e) => {
    const day = +e.target.value;
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + day);
    setFollowUpDate(expiryDate);
  };

  useEffect(() => {
    const handleCustomerAdded = async () => {
      const customersData = await getCustomers().unwrap();
      setCustomers(customersData?.customers);
      setSelectedCustomer(
        customersData?.customers?.[customersData?.customers?.length - 1]?._id
      );

      setCustomerData((prev) => {
        return {
          ...prev,
          company_name:
            customersData?.customers?.[customersData?.customers?.length - 1]
              ?.company_name,
        };
      });
    };
    if (isAdded) {
      handleCustomerAdded();
    }
  }, [isAdded]); // eslint-disable-line

  const renderFormField = (label, children, htmlFor) => (
    <div className="space-y-2">
      <label htmlFor={htmlFor} className="block text-sm font-semibold text-gray-700">
        {label}
      </label>
      {children}
    </div>
  );

  return (
    <>
      {OpenCamera ? (
        <CaptureCamera
          setpdf={setpdf}
          capturedImage={capturedImage}
          setImageInfo={setCapturedImage}
          setOpenCamera={setOpenCamera}
        />
      ) : (
          <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-7xl">
            <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
              <div className="flex items-center gap-3">
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={() => {
                    setIsAdd(false);
                    setIsCopy(false);
                  }}
                  type="text"
                  size="small"
                  className="hover:bg-gray-200"
                />
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-0">
                    {isEdit ? 'Edit' : 'Create'} Sales Inquiry
                  </h2>
                  <p className="text-sm text-gray-600 mb-0">
                    {isEdit ? 'Update sales inquiry information' : 'Create a new sales inquiry'}
                  </p>
                </div>
              </div>
            </div>

            <RightSidebar openSideBar={ShowMetaDataSidebar} setOpenSideBar={setShowMetaDataSidebar}>
              <MediaMetaDataCard pdf={pdf} />
          </RightSidebar>

          {ShowEditMetaData && (
            <MediaModal
              FormData={pdf}
              setFormData={setpdf}
              setShowModal={setShowEditMetaData}
              ShowModal={ShowEditMetaData}
            />
          )}
          {openAddUomModal && (
            <AddUomModal
              setShowModal={setOpenAddUomModal}
              dropdowns={dropdownsData?.dropdowns}
            />
          )}
          {ShowCustomerModal && (
            <CustomerModalFormData
              isMobile={isMobile}
              isTablet={isTablet}
              CustomerData={CustomerData}
              setCustomerData={setCustomerData}
              ColumnsValue={ColumnsValue}
              setColumnsValue={setColumnsValue}
              setShowAddCustomer={setShowCustomerModal}
              getResponse={getResponse}
              getCustomers={getCustomers}
              setIsAdded={setIsAdded}
              setCustomers={setCustomers}
              type={'Add'}
              title={'Add New Customer'}
            />
          )}

            <div className="space-y-6 p-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex items-center gap-3">
                <Input
                  type="checkbox"
                  checked={showForms}
                  id="selectOrderForms"
                    onChange={() => setShowForms((prev) => !prev)}
                />
                  <Label htmlFor="selectOrderForms" className="text-sm font-medium text-gray-700">
                  Select from Order Forms
                </Label>
              </div>

                <div className="flex flex-col sm:flex-row gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-gray-800">Date:</span>
                    <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      {new Date().toLocaleDateString('en-in', {
                        month: 'long',
                        year: 'numeric',
                        day: 'numeric',
                      })}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-gray-800">Task ID:</span>
                    <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      {taskId}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
                {renderFormField(
                  'Sales Inquiry ID:',
                  <div className="w-full md:w-2/5">
                    <IdGenComp {...idCompData} />
                  </div>
                )}
            </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  {renderFormField(
                    'Choose Template',
                  <SelectV2
                    options={templatesData?.map((template) => ({
                      value: template,
                      name: template.name,
                    }))}
                    onChange={(e) => {
                      if (selectedTemplate === e.target.value) return;
                      setAdditionalFields(e.target.value);
                      setSelectedTemplate(e.target.value);
                    }}
                    value={selectedTemplate}
                    className="w-full"
                  />
                  )}
              </div>

                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  {renderFormField(
                    'Sales Inquiry Name',
                    <Input
                      value={inquiryName}
                      onChange={(e) => setInquiryName(e.target.value)}
                      className="w-full"
                    />
                  )}
              </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  {renderFormField(
                    'Industry Type',
                    <Input
                      value={industryType}
                      onChange={(e) => setIndustryType(e.target.value)}
                      className="w-full"
                    />
                  )}
              </div>

                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  {renderFormField(
                    'Follow Up',
                    followUpDate === '+' ? (
                      <Input
                        type="date"
                        className="w-full"
                        placeholder="Follow Up Date"
                        min={minDate}
                        onChange={(e) => setFollowUpDate(e.target.value)}
                      />
                    ) : (
                      <SelectV2
                        options={dateOptions}
                        className="w-full"
                        styles={{
                            placeholder: (provided) => ({ ...provided, color: '#000000' }),
                          }}
                          placeholder={
                            followUpDate
                              ? generateDateString(new Date(followUpDate))
                              : 'Select'
                          }
                          onChange={(e) => {
                            if (e.value === '+') {
                              setFollowUpDate(e.value);
                            } else {
                              handleSetDate(e);
                            }
                          }}
                        />
                      )
                )}
              </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {showForms ? (
                <>
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      {renderFormField(
                        'Forms',
                        <SelectV2
                          placeholder="Order Forms"
                          onChange={(e) => {
                            setSelectedForm(e.target.value);
                            setSelectedResponse('');
                            setCompanyName('');
                            setRemark('');
                          }}
                          value={selectedForm}
                          closeMenuOnSelect={true}
                          options={forms.map((form, index) => ({
                          value: form?._id,
                          label: form?.company_name,
                          key: index,
                          }))}
                          className="w-full"
                        />
                      )}
                  </div>
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      {renderFormField(
                        'Select Company Name',
                        <SelectV2
                          placeholder="Form Responses"
                          onChange={(e) => {
                            setSelectedResponse(e.target.value);
                            setCompanyName(e.target.value?.company_name);
                            setRemark(e.target.value?.data['Remark']?.value);
                            setCustomerData(e.target.value);
                            PdfHandler(e.target.value?.data['Files']);
                            handleAddProducts(e.target.value?.data['Products']);
                          }}
                          value={selectedResponse}
                          closeMenuOnSelect={true}
                          options={response
                            .filter((res) => res?.form_id?._id === selectedForm)
                            .map((res, index) => ({
                            value: res,
                              label: res?.company_name,
                            key: index,
                            }))}
                          className="w-full"
                        />
                      )}
                  </div>
                </>
              ) : (
                <>
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        {renderFormField(
                          'Choose Customer',
                          <SelectV2
                            isLoading={isLoading}
                            options={[
                              { value: 'add new', label: '+Add new customer' },
                              ...customers.map((customer) => ({
                                value: customer?._id || customer.value,
                                label:
                                  customer?.label ||
                                  `${customer?.company_name ? `${customer.company_name} - ` : ''}${customer?.name || ''}`,
                              })),
                            ]}
                            value={selectedCustomer}
                            onChange={(e) => handleSelectCustomer(e.target.value)}
                            loadingMessage={() => <Spinner size={6} />}
                            className="w-full"
                          />
                        )}
                  </div>
                      {selectedCustomer && (
                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                          {renderFormField(
                            'Company Name',
                            <Input
                              className="w-full"
                              disabled
                              value={CustomerData?.company_name}
                            />
                          )}
                    </div>
                  )}
                </>
              )}
              </div>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <h3 className="text-sm font-semibold text-gray-700 mb-4">Products</h3>
                <div className="overflow-x-auto border rounded-md shadow-sm">
                  <Table className="w-full">
                  <Table.Head>
                    <Table.Row className="bg-gray-50">
                        <Table.Th className="border-r">Product Name</Table.Th>
                        <Table.Th className="border-r">Quantity</Table.Th>
                        <Table.Th className="border-r">UOM</Table.Th>
                        <Table.Th>Action</Table.Th>
                    </Table.Row>
                  </Table.Head>
                    <Table.Body>
                    {products.map((product, index) => (
                      <Table.Row key={index}>
                        <Table.Td className="!relative border-r">
                          {product.manualEntry ? (
                            <Input
                              value={product.productName || ''}
                              onChange={(e) => {
                                const updatedProducts = [...products];
                                updatedProducts[index] = {
                                  ...updatedProducts[index],
                                  productName: e.target.value,
                                  manualEntry: true,
                                };
                                setProducts(updatedProducts);
                              }}
                              placeholder="Enter Product Name"
                            />
                          ) : (
                            <SelectV2
                              size="small"
                              options={[
                                { label: '+Add manual product', value: '+' },
                                ...inventoryProducts.map((p) => ({
                                  label: p.name,
                                  value: p.name,
                                })),
                              ]}
                              value={product.productName}
                              onChange={(e) => {
                                const updatedProducts = [...products];
                                if (e.target.value === '+') {
                                  updatedProducts[index] = { manualEntry: true };
                                } else {
                                  const selectedProduct = inventoryProducts.find(
                                    (p) => p.name === e.target.value
                                  );
                                  updatedProducts[index] = {
                                    ...updatedProducts[index],
                                    productName: selectedProduct.name,
                                    uom: selectedProduct.uom,
                                    manualEntry: false,
                                    productId: selectedProduct?._id,
                                  };
                                }
                                setProducts(updatedProducts);
                              }}
                              menuPortalTarget={document.body}
                              menuPosition="fixed"
                              placeholder="Select Product"
                            />
                          )}
                        </Table.Td>
                        <Table.Td className="border-r">
                          <Input
                            type="number"
                            value={product.quantity}
                            min={0}
                            onChange={(e) => {
                              const updatedProducts = [...products];
                              updatedProducts[index] = {
                                ...updatedProducts[index],
                                quantity: e.target.value,
                              };
                              setProducts(updatedProducts);
                            }}
                            placeholder="Enter quantity"
                          />
                        </Table.Td>
                        <Table.Td className="!relative border-r">
                          <SelectV2
                            placeholder="Select UOM"
                            menuPortalTarget={document.body}
                            menuPosition="fixed"
                            size="small"
                            value={product.uom}
                            options={[
                              { label: '+Add new UOM', value: '+' },
                              ...(dropdownsData?.dropdowns
                                ?.find((e) => e.name === 'uom')
                                ?.values?.map((el, idx) => ({
                                  value: el,
                                  label: el,
                                  key: idx,
                                })) || []),
                            ]}
                            onChange={(e) => {
                              if (e.target.value === '+') {
                                setOpenAddUomModal(true);
                              } else {
                                const updatedProducts = [...products];
                                updatedProducts[index] = {
                                  ...updatedProducts[index],
                                  uom: e.target.value,
                                };
                                setProducts(updatedProducts);
                              }
                            }}
                          />
                        </Table.Td>
                        <Table.Td>
                          <Button
                            danger
                            size="small"
                            onClick={() => handleRemoveProduct(index)}
                            icon={<DeleteOutlined />}
                          />
                        </Table.Td>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
                <Button type="primary" size="small" onClick={handleAddProduct} className="mt-4">
                + Add Product
              </Button>
            </div>

              {additionalFields?.templateData?.length > 0 && (
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-700 mb-4">Additional Fields</h3>
                  {renderFieldsBasedOnType(
                    additionalFields,
                    handleInputChange,
                    templateDropDownModal,
                    setTemplateDropDownModal,
                    setAdditionalFields,
                    newOptionStatus,
                    setNewOptionStatus,
                    dropdownIdx,
                    setDropdownIdx,
                    Searchparams
                  )}
                </div>
              )}

              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <h3 className="text-sm font-semibold text-gray-700 mb-4">Remarks & Files</h3>
                <div className="space-y-4">
                  {renderFormField(
                    'Remarks',
                    <Textarea
                      value={remark}
                      onChange={(e) => setRemark(e.target.value)}
                    />
                  )}

                  <div className="flex gap-4 items-center">
                    <div className='w-full'>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Select File
                    </label>
                    <UploadButton
                        accept="*"
                      onChange={(e) => changeHandler(e, 'project')}
                      multiple
                        width='w-full'
                    />
                  </div>

                  {isMobile && (
                      <>
                        <span className="text-blue-500 mt-4">OR</span>
                        <CustomToolTip tooltipId="camera-id" content="Camera" place="top">
                          <div
                            className="cursor-pointer mt-4 hover:bg-gray-200 p-2 rounded-full"
                            onClick={() => {
                              setCapturedImage([]);
                              setOpenCamera((prev) => !prev);
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                              className="size-5 text-gray-400 hover:text-blue-400"
                            >
                              <path
                                fillRule="evenodd"
                                d="M1 8a2 2 0 0 1 2-2h.93a2 2 0 0 0 1.664-.89l.812-1.22A2 2 0 0 1 8.07 3h3.86a2 2 0 0 1 1.664.89l.812 1.22A2 2 0 0 0 16.07 6H17a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8Zm13.5 3a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM10 14a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        </CustomToolTip>
                      </>
                  )}
                </div>

                  {pdf?.length > 0 && (
                    <div className="mt-4">
                      <Table>
                        <Table.Head>
                          <Table.Row>
                            <Table.Th>Files</Table.Th>
                            <Table.Th>Description</Table.Th>
                            <Table.Th>
                              <EditButton onClick={() => setShowEditMetaData(true)} />
                            </Table.Th>
                          </Table.Row>
                        </Table.Head>
                        <Table.Body>
                          {pdf.map((item, idx) => (
                            <Table.Row key={idx}>
                              <Table.Td>{item.name}</Table.Td>
                              <Table.Td>
                                {item?.description?.length > 20 ? (
                                  <>
                                    <span className="break-words">
                                      {item.description.slice(0, 20)}
                                    </span>
                                    <p
                                      className="text-blue-500 cursor-pointer inline-block ml-2"
                                      onClick={() => setShowMetaDataSidebar(true)}
                                    >
                                      Read More...
                                    </p>
                                  </>
                                ) : (
                                  item.description
                                )}
                              </Table.Td>
                              <Table.Td
                                onClick={() => removePdf(item.name)}
                                className="cursor-pointer"
                              >
                                <Button
                                  danger
                                  size="small"
                                  icon={<DeleteOutlined />}
                                />
                              </Table.Td>
                            </Table.Row>
                          ))}
                        </Table.Body>
                      </Table>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
              <div className="flex items-center justify-end">
                <Button
                  loading={isAddSalesInquiryLoading}
                  onClick={handleSubmitModal}
                  type="primary"
                >
                  Submit
                </Button>
              </div>
            </div>
        </div>
      )}
    </>
  );
}
