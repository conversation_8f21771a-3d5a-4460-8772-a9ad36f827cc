import { useEffect, useState } from 'react';
import { useLazyGetAllcustomerQuery } from '../../../slices/customerDataSlice.js';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { useLazyGetResponseQuery } from '../../../slices/online/createOrderFormApiSlice';
import MasterDetails from '../../MasterDetails.jsx';
import CustomerModalFormData from '../../global/components/CustomerModalFormData.jsx';
import Select from '../../global/components/Select';

import Spinner from '../../global/components/Spinner.jsx';
import AddPaymentTerm from '../../v3/InventoryMasters/AddPaymentTerm.jsx';

const VendorDetails = ({
  formData,
  setFormData,
  isEdit,
  isMobile,
  isTablet,
}) => {
  // eslint-disable-next-line no-unused-vars
  const [getAllCustomers, { isLoading: isCustomerLoading }] =
    useLazyGetAllcustomerQuery();
  // eslint-disable-next-line no-unused-vars
  const [getResponse, { isLoading: isResponseLoading }] =
    useLazyGetResponseQuery();
  const { data: dropdowns } = useGetDropdownsQuery();
  const [isLoading, setIsLoading] = useState(false);
  const [Vdata, setVdata] = useState([]);
  const [ShowPayMentModal, setShowPaymentTermModal] = useState(false);
  const [response, setResponse] = useState({});
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [CustomerData, setCustomerData] = useState({
    name: '',
    company_name: '',
    unique_id: '',
    phone_no: '',
    address: '',
    gstNumber: [''],
    billingAddress: '',
    deliveryAddress: '',
    paymentTerm: '',
  });
  const [ColumnsValue, setColumnsValue] = useState({});
  const [PaymentTermOptions, setPaymentTermOptions] = useState([]);
  useEffect(() => {
    if (!dropdowns) return;
    const PaymentTerm = dropdowns?.dropdowns?.find((dropdown) => {
      return dropdown?.name === 'Payment Term';
    });
    setPaymentTermOptions(PaymentTerm?.values);
  }, [dropdowns]);

  /**
   * The handleChange function updates the formData state with the details of a selected vendor.
   */
  useEffect(() => {
    const getVendorData = async () => {
      setIsLoading(true);
      try {
        const data1 = await getAllCustomers().unwrap();
        const addOption = { value: 'add new', label: '+ Add new customer' };
        setVdata([addOption, ...data1?.customers]);

        // const data2 = await getResponse(
        //   { field_name: "", field_value: "" },
        //   false
        // ).unwrap();
        // console.log(data2);
      } catch (error) {
        // console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    getVendorData();
  }, [getAllCustomers, getResponse]);

  useEffect(() => {
    if (!isEdit) {
      if (response?._id) {
        setVdata((prev) => [...prev, response]);
        setFormData((prev) => ({
          ...prev,
          vendorDetails: {
            ...prev.vendorDetails,
            id: response?._id,
            name: response?.name,
            companyName: response?.company_name,
            email: response?.unique_id,
            address: response?.address,
            mobileNumber: response?.phone_no,
            gstNumber: response?.gstNumber,
          },
        }));
      }
    }
  }, [response]); //eslint-disable-line

  const handleChange = async (e) => {
    if (e.target.value === 'add new') {
      setShowAddCustomer(true);
    } else {
      const vendorData = Vdata.find((item) => {
        return item?._id === e.target.value;
      });
      setFormData((prev) => ({
        ...prev,
        vendorDetails: {
          ...prev.vendorDetails,
          id: vendorData?._id,
          name: vendorData?.name,
          companyName: vendorData?.company_name,
          email: vendorData?.unique_id,
          address: vendorData?.address,
          billingAddress: vendorData?.billingAddress,
          mobileNumber: vendorData?.phone_no,
          gstNumber: vendorData?.gstNumber,
          paymentTerm: vendorData?.paymentTerm,
        },
      }));
    }
  };

  return (
    <div className="space-y-3">
      {ShowPayMentModal && (
        <AddPaymentTerm
          isMobile={isMobile}
          isTablet={isTablet}
          setShowModal={setShowPaymentTermModal}
          dropdowns={dropdowns}
          PaymentTermOptions={PaymentTermOptions}
        />
      )}
      {showAddCustomer && (
        <CustomerModalFormData
          isMobile={isMobile}
          isTablet={isTablet}
          CustomerData={CustomerData}
          setCustomerData={setCustomerData}
          ColumnsValue={ColumnsValue}
          setColumnsValue={setColumnsValue}
          setShowAddCustomer={setShowAddCustomer}
          getResponse={getResponse}
          type={'Add'}
          title={'Add New Customer'}
          setResponse={setResponse}
        />
      )}

      <h3 className="text-sm font-semibold text-gray-700">Customer Details:</h3>
      <div className="w-full !md:w-3/5">
        <Select
          placeholder="Choose Customer"
          name="vendorDetails"
          menuPlacement="auto"
          className="w-full"
          onChange={(e) => {
            handleChange(e);
          }}
          isLoading={isLoading}
          value={formData?.vendorDetails?.id || ''}
          closeMenuOnSelect={true}
          loadingMessage={() => <Spinner size={6} />}
          options={Vdata?.map((el, index) => ({
            value: el?._id || el.value,
            label:
              el?.label ||
              `${el?.company_name ? `${el.company_name} - ` : ''}${el?.name || ''}`,
            key: index,
          }))}
        />
      </div>

      <MasterDetails
        isMobile={isMobile}
        isTablet={isTablet}
        details={
          formData?.vendorDetails?.id !== '' ? formData?.vendorDetails : {}
        }
        excludedFields={[
          'id',
          '_id',
          'logo',
          '__v',
          'profileId',
          'createdAt',
          'updatedAt',
        ]}
        setDetails={setFormData}
        keyToEdit={'vendorDetails'}
        className="p-3 bg-gray-50 rounded border"
      />
    </div>
  );
};

export default VendorDetails;
