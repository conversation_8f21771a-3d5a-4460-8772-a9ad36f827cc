import { useEffect, useState } from 'react';
import { BiH<PERSON>, BiShow } from 'react-icons/bi';
import { fileHand<PERSON>, getDecodedHTML } from '../../../helperFunction.js';
import Input from '../../global/components/Input.jsx';
import MediaViewer from '../../global/components/MediaViewer.jsx';
import RightSidebar from '../../global/components/RightSidebar.jsx';
import Table from '../../global/components/Table.jsx';
import TermsAndConditionsSelect from '../../global/components/TermsAndConditionsSelect.jsx';
import MasterDetails from '../../MasterDetails.jsx';
import UploadButton from '../../UploadButton.jsx';
import { Label } from '../../v2/index.js';
import EditButton from '../../v3/global/components/EditButton.jsx';
import MediaMetaDataCard from '../../v3/global/components/MediaMetaDataCard.jsx';
import MediaModal from '../../v3/global/components/MediaModal.jsx';
import RichTextDescription from '../../v3/global/components/rich-text-description.jsx';

const saveToLocalStorage = (key, data) => {
  localStorage.setItem(key, JSON.stringify(data));
};

const getFromLocalStorage = (key) => {
  const data = localStorage.getItem(key);
  return data ? JSON.parse(data) : [];
};
const TermsAndConditions = ({
  isMobile,
  isTablet,
  formData,
  setFormData,
  isEdit,
  setDefaultAdditionalComments,
  defaultAdditionalComments,
  selectedTermAndCondition,
  setSelectedTermAndCondition,
  isSetAsDefault,
  page,
  quoteID,
  bankDetailsSelector,
}) => {
  const [viewMediaData, setViewMediaData] = useState({});
  const [ShowModal, setShowModal] = useState(false);
  const [ShowDescriptionSidebar, setShowDescriptionSidebar] = useState(false);
  const [isImageOnTop, setIsImageOnTop] = useState(false);

  const handleFileChange = async (e) => {
    const medias = await fileHandler(e);
    setFormData((prev) => ({
      ...prev,
      attachments: [...(prev?.attachments || []), ...medias],
    }));
  };

  useEffect(() => {
    const localStorageData = getFromLocalStorage('imageOnTop');
    const newStoredData = [
      ...localStorageData.filter((item) => item.id !== quoteID),
      { id: quoteID, isImageOnTop: isImageOnTop },
    ];
    saveToLocalStorage('imageOnTop', newStoredData);
  }, [isImageOnTop, quoteID]);

  const handleCheckboxChange = (e) => {
    setIsImageOnTop(e.target.checked);
  };

  const removeFileHandler = (idx) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev?.attachments?.filter((_, index) => index !== idx),
      removedAttachments: isEdit
        ? [...(prev?.removedAttachments || []), prev?.attachments?.[idx]?._id]
        : [],
    }));
  };
  const imageMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const images = formData?.attachments?.filter((attach) =>
    imageMimeTypes.includes(attach?.type)
  );

  const toggleBankDetails = () => {
    setFormData((prev) => ({
      ...prev,
      bankDetailsHideStatus: !prev.bankDetailsHideStatus,
    }));
  };

  return (
    <>
      <RightSidebar
        openSideBar={ShowDescriptionSidebar}
        setOpenSideBar={setShowDescriptionSidebar}
      >
        <MediaMetaDataCard pdf={formData?.attachments} />
      </RightSidebar>
      {ShowModal && (
        <MediaModal
          FormData={formData}
          setFormData={setFormData}
          keyName={'attachments'}
          setShowModal={setShowModal}
          ShowModal={ShowModal}
        />
      )}
      {viewMediaData?.data && (
        <MediaViewer media={viewMediaData} setMedia={setViewMediaData} />
      )}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="p-6">
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-semibold text-gray-700">
              Bank Details
            </label>
            <button
              onClick={toggleBankDetails}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            >
              {formData?.bankDetailsHideStatus ? (
                <BiHide size={20} />
              ) : (
                <BiShow size={20} />
              )}
            </button>
          </div>

          <div className="space-y-4">
            <Input
              disabled
              value={formData?.bankDetails?.bankName}
              className="bg-gray-50"
            />
            <MasterDetails
              companySelectors={{ bank: bankDetailsSelector }}
              details={formData?.bankDetails || {}}
              excludedFields={[]}
              className="p-4 bg-gray-50 rounded-lg border"
            />
          </div>
        </div>
      </div>
      <div className="w-full text-sm text-gray-500 font-medium">
        <div className="flex flex-col my-5 w-full bg-white p-4 rounded-lg border border-gray-200">
          <label className="block text-sm font-semibold text-gray-700">
            Terms and Conditions :
          </label>
          <TermsAndConditionsSelect
            isMobile={isMobile}
            isTablet={isTablet}
            selectedTermAndCondition={selectedTermAndCondition}
            setSelectedTermAndCondition={setSelectedTermAndCondition}
            isEdit={isEdit}
            editValue={formData?.termsAndConditions}
            page={page}
            isSetAsDefault={isSetAsDefault}
            formData={formData}
            setformData={setFormData}
          />
        </div>
        <div className="flex flex-col my-5 w-full bg-white p-4 rounded-lg border border-gray-200">
          <label className="block text-sm font-semibold text-gray-700">
            Additional Comments :
          </label>
          <RichTextDescription
            value={getDecodedHTML(formData?.additionalComments)}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                additionalComments: e,
              }));
            }}
          />
          <div
            className="flex items-center gap-3 justify-end mr-5 mt-2"
            id="ignore"
          >
            <Input
              type="checkbox"
              checked={defaultAdditionalComments}
              id="ignore"
              onChange={() => {
                setDefaultAdditionalComments((prev) => !prev);
              }}
            />
            <Label>Set as default</Label>
          </div>
        </div>
        <div className="w-full col-span-full bg-white p-4 rounded-lg border border-gray-200">
          <label className="block text-sm font-semibold text-gray-700">
            Attachments
          </label>
          <UploadButton
            accept="*"
            onChange={handleFileChange}
            className="w-full"
            multiple
            width="w-full"
            maxSizeMB={4}
          />
          {/* Attachment Check */}
          {formData?.attachments?.length > 0 && images.length > 0 && (
            <div className="flex justify-end p-2 gap-2">
              <input onChange={handleCheckboxChange} type="checkbox" />
              <label>Image on Top</label>
            </div>
          )}
          <section className="w-full flex gap-x-5 overflow-x-scroll mt-4">
            {formData?.attachments?.length > 0 && (
              <>
                <Table>
                  <Table.Head>
                    <Table.Row>
                      <Table.Th>File</Table.Th>
                      <Table.Th>Description</Table.Th>
                      <Table.Th>
                        <EditButton
                          onClick={() => {
                            setShowModal(true);
                          }}
                        />
                      </Table.Th>
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {formData?.attachments?.map((file, idx) => {
                      return (
                        <Table.Row key={idx}>
                          <Table.Td>{file?.name}</Table.Td>
                          {file?.description?.length > 50 ? (
                            <Table.Td>
                              {file?.description?.slice(0, 51)}{' '}
                              <span
                                className="text-blue-500 cursor-pointer inline-block"
                                onClick={() => {
                                  setShowDescriptionSidebar(true);
                                }}
                              >
                                <span className="ml-5">Read More...</span>
                              </span>
                            </Table.Td>
                          ) : (
                            <Table.Td>{file?.description}</Table.Td>
                          )}
                          <Table.Td
                            className="!cursor-pointer"
                            onClick={() => {
                              removeFileHandler(idx);
                            }}
                          >
                            x
                          </Table.Td>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              </>
            )}
          </section>
        </div>
      </div>
    </>
  );
};

export default TermsAndConditions;
