import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  dateOptions,
  generateDateString,
  transformAddressField,
} from '../../../helperFunction.js';
import usePrefixIds from '../../../hooks/usePrefixIds.js';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice.js';
import { useGetLatestQuotationQuery } from '../../../slices/quotationApiSlice.js';
import { useGetSalesInquiriesQuery } from '../../../slices/salesInquiryDashboardApiSlice.js';
import MasterDetails from '../../MasterDetails.jsx';
import Input from '../../global/components/Input';
import Select from '../../global/components/Select.jsx';
import { Label } from '../../v2/index.js';
import VendorDetails from './VendorDetails';

const QuoteDetails = ({
  isMobile,
  isTablet,
  formData,
  setFormData,
  isEdit,
  setSelectedSalesInquiry,
  quotation,
  additionalFields,
  setAdditionalFields,
  addressSelector,
  contactSelector,
  profileSelector,
  emailSelector,
  getIdOptions,
  setQuotationVersionFormatComponent,
  setQuotationVersionFormat,
  quotationVersionFormatArray,
  setVersionIndex,
  quotationVersionFormatComponent,
  versionIndex,
  setAdditionalFieldsHideStatus,
}) => {
  const { data } = useGetSalesInquiriesQuery();
  const [showSI, setShowSI] = useState(false);
  const [DateValue, setDateValue] = useState('');
  const { data: latestQuotation } = useGetLatestQuotationQuery();

  const { IdGenComp, idCompData, taskId } = usePrefixIds({
    idFor: 'quotationId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });

  useEffect(() => {
    if (formData?.SalesInquiryLink?.length > 0) {
      setShowSI(true);
    }
  }, [formData?.SalesInquiryLink]);

  const handleChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      productDetails: [],
    }));
    const salesInquiry = data?.find((item) => item?._id === e.target.value);
    setSelectedSalesInquiry(salesInquiry);
    setFormData((prev) => {
      const arr = prev.productDetails.filter((item) => !item._id);
      return {
        ...prev,
        SalesInquiryLink: salesInquiry?._id,
        SalesInquiryRef: e.target.value,
        productDetails: [
          ...arr,
          ...salesInquiry?.products?.map((el) => {
            return {
              ...el,
              rate: 0,
              amount: 0,
              totalAmount: 0,
              cgst: 0,
              sgst: 0,
              igst: 0,
              discount: 0,
              HsnSacCode: 0,
              attachments: [],
            };
          }),
        ],
        vendorDetails: {
          ...prev.vendorDetails,
          id: salesInquiry?.CustomerData?._id,
          name: salesInquiry?.CustomerData?.name,
          companyName: salesInquiry?.CustomerData?.company_name,
          email: salesInquiry?.CustomerData?.unique_id,
          address: salesInquiry?.CustomerData?.address,
          mobileNumber: salesInquiry?.CustomerData?.phone_no,
          gstNumber: salesInquiry?.CustomerData?.gstNumber,
        },
      };
    });
  };

  useEffect(() => {
    if (latestQuotation && !isEdit) {
      const { productColumnHideStatus, customColumnsHideStatus } =
        latestQuotation;
      setFormData((prev) => ({
        ...prev,
        productColumnHideStatus,
        customColumnsHideStatus,
      }));
    }
  }, [latestQuotation]); //eslint-disable-line

  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [selectedTemplate, setSelectedTemplate] = useState(
    isEdit ? formData?.additionalFields : null
  );

  useEffect(() => {
    const getCols = async () => {
      const path = '/salesordermanagement/quotation';
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);
  const [Searchparams] = useSearchParams();

  useEffect(() => {
    if (isEdit) return;
    if (!quotation || !templatesData) return;
    if (quotation?.length === 0) {
      const defaultTemplate = templatesData?.find((template) => {
        return template?.name?.startsWith('Default');
      });
      setSelectedTemplate(defaultTemplate);
      setAdditionalFields(defaultTemplate);
      return;
    } else {
      const templateParamsId =
        Searchparams.get('templateId') === 'undefined'
          ? null
          : Searchparams.get('templateId');
      const quotationData = [...quotation].sort((a, b) => {
        const dateA = new Date(a.createdAt);
        const dateB = new Date(b.createdAt);
        return dateB - dateA;
      });
      const previouslyUsedTemplate = quotationData[0]?.additionalFields;
      const templateToUse = templatesData?.find((template) => {
        return (
          template?._id ===
          (templateParamsId ? templateParamsId : previouslyUsedTemplate?._id)
        );
      });
      if (templateToUse) {
        setSelectedTemplate(templateToUse);
        setAdditionalFields(templateToUse);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templatesData, Searchparams, quotation]);

  const handleSetDate = (e) => {
    const day = +e.target.value;
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + day);
    setDateValue(expiryDate);
    setFormData((prev) => ({
      ...prev,
      date: {
        ...prev.date,
        expiryDate: expiryDate,
      },
    }));
  };

  useEffect(() => {
    if (isEdit) {
      if (formData?.SalesInquiryLink) {
        setShowSI(true);
      } else {
        setShowSI(false);
      }
    }
  }, [isEdit, formData?.SalesInquiryLink]);

  const renderFormField = (label, children, htmlFor) => (
    <div className="space-y-2">
      <label
        htmlFor={htmlFor}
        className="block text-sm font-semibold text-gray-700"
      >
        {label}
      </label>
      {children}
    </div>
  );

  return (
    <div className="bg-white">
      <div className="space-y-6 p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center bg-white p-4 rounded-lg border border-gray-200">
          {!isEdit && (
            <div className="flex items-center gap-3">
              <Input
                type="checkbox"
                checked={showSI}
                id="selectFromSI"
                onChange={() => {
                  setShowSI((prev) => !prev);
                }}
              />
              <Label
                htmlFor="selectFromSI"
                className="text-sm font-medium text-gray-700"
              >
                Select from Sales Inquiry
              </Label>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4 text-sm">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-gray-800">Date:</span>
              <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">
                {new Date().toLocaleDateString('en-in', {
                  month: 'long',
                  year: 'numeric',
                  day: 'numeric',
                })}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-semibold text-gray-800">Task ID:</span>
              <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">
                {taskId}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-semibold text-gray-700">
              Quote ID:
            </label>
            <div className="w-full md:w-2/5">
              {isEdit ? (
                <Input
                  disabled
                  value={formData?.quoteID}
                  className="bg-gray-50"
                />
              ) : (
                <IdGenComp {...idCompData} />
              )}
            </div>
          </div>

          {isEdit && (
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Quotation Version
              </label>
              <div className="border-2 rounded-md py-1 px-2 bg-gray-100 w-full md:w-2/5">
                {Object.keys(quotationVersionFormatComponent).map((elem) => (
                  <div key={elem}>{quotationVersionFormatComponent[elem]}</div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            {renderFormField(
              'Choose Template',
              <Select
                options={templatesData?.map((template) => ({
                  value: template._id,
                  name: template.name,
                }))}
                onChange={(e) => {
                  const template = templatesData.find(
                    (t) => t._id === e.target.value
                  );
                  if (selectedTemplate?._id === e.target.value) {
                    return;
                  }
                  setAdditionalFields(template);
                  setSelectedTemplate(template);
                  setAdditionalFieldsHideStatus({});
                  if (selectedTemplate?.idIndex === template.idIndex) {
                    return;
                  }
                }}
                value={selectedTemplate?._id}
                className="w-full"
              />
            )}
          </div>

          {isEdit && (
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              {renderFormField(
                'Select Version',
                <Select
                  options={getIdOptions()}
                  onChange={(e) => {
                    setQuotationVersionFormat(e.target.value);
                    setQuotationVersionFormatComponent({});
                    let index = -1;
                    for (let i in quotationVersionFormatArray) {
                      if (
                        JSON.stringify(quotationVersionFormatArray[i]) ===
                        JSON.stringify(e.target.value)
                      ) {
                        index = parseInt(i);
                        break;
                      }
                    }
                    setVersionIndex(index);
                  }}
                  value={quotationVersionFormatArray?.[versionIndex]}
                  className="w-full"
                />
              )}
            </div>
          )}

          {!isEdit && (
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              {renderFormField(
                'Quote Expiry Date',
                DateValue === '+' ? (
                  <Input
                    type="date"
                    className="w-full"
                    id="quoteExpiryDate"
                    name="quoteExpiryDate"
                    placeholder="Quote Expiry Date"
                    min={isEdit ? '' : new Date().toISOString().split('T')[0]}
                    value={
                      formData?.date?.expiryDate
                        ? new Date(formData?.date?.expiryDate)
                            .toISOString()
                            .split('T')[0]
                        : ''
                    }
                    onChange={(e) => {
                      const selectedDate = new Date(e.target.value);
                      setFormData((prev) => ({
                        ...prev,
                        date: {
                          ...prev.date,
                          expiryDate: selectedDate,
                        },
                      }));
                    }}
                  />
                ) : (
                  <Select
                    options={dateOptions}
                    className="w-full"
                    placeholder={
                      formData?.date?.expiryDate
                        ? generateDateString(
                            new Date(formData?.date?.expiryDate)
                          )
                        : 'Select'
                    }
                    styles={{
                      placeholder: (provided) => ({
                        ...provided,
                        color: '#000000',
                      }),
                    }}
                    onChange={(e) => {
                      if (e.target.value === '+') {
                        setFormData((prev) => ({
                          ...prev,
                          date: {
                            ...prev.date,
                            expiryDate: '',
                          },
                        }));
                        setDateValue(e.target.value);
                      } else {
                        handleSetDate(e);
                      }
                    }}
                  />
                ),
                'quoteExpiryDate'
              )}
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {isEdit && (
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              {renderFormField(
                'Quote Expiry Date',
                DateValue === '+' ? (
                  <Input
                    type="date"
                    className="w-full"
                    id="quoteExpiryDate"
                    name="quoteExpiryDate"
                    placeholder="Quote Expiry Date"
                    min={isEdit ? '' : new Date().toISOString().split('T')[0]}
                    value={
                      formData?.date?.expiryDate
                        ? new Date(formData?.date?.expiryDate)
                            .toISOString()
                            .split('T')[0]
                        : ''
                    }
                    onChange={(e) => {
                      const selectedDate = new Date(e.target.value);
                      setFormData((prev) => ({
                        ...prev,
                        date: {
                          ...prev.date,
                          expiryDate: selectedDate,
                        },
                      }));
                    }}
                  />
                ) : (
                  <Select
                    options={dateOptions}
                    className="w-full"
                    placeholder={
                      formData?.date?.expiryDate
                        ? generateDateString(
                            new Date(formData?.date?.expiryDate)
                          )
                        : 'Select'
                    }
                    styles={{
                      placeholder: (provided) => ({
                        ...provided,
                        color: '#000000',
                      }),
                    }}
                    onChange={(e) => {
                      if (e.target.value === '+') {
                        setFormData((prev) => ({
                          ...prev,
                          date: {
                            ...prev.date,
                            expiryDate: '',
                          },
                        }));
                        setDateValue(e.target.value);
                      } else {
                        handleSetDate(e);
                      }
                    }}
                  />
                ),
                'quoteExpiryDate'
              )}
            </div>
          )}

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            {renderFormField(
              'Follow Up',
              DateValue === 'FollowUpTime' ? (
                <Input
                  type="date"
                  className="w-full"
                  id="followUp"
                  name="followUpTime"
                  placeholder="Follow Up Time"
                  value={
                    formData?.followUpTime
                      ? formData?.followUpTime.toISOString().split('T')[0]
                      : ''
                  }
                  min={isEdit ? '' : new Date().toISOString().split('T')[0]}
                  onChange={(e) => {
                    const date = new Date(e.target.value);
                    setFormData((prev) => ({
                      ...prev,
                      followUpTime: date,
                    }));
                  }}
                />
              ) : (
                <Select
                  options={dateOptions}
                  className="w-full"
                  styles={{
                    placeholder: (provided) => ({
                      ...provided,
                      color: '#000000',
                    }),
                  }}
                  placeholder={
                    formData?.followUpTime
                      ? generateDateString(new Date(formData?.followUpTime))
                      : 'Select'
                  }
                  onChange={(e) => {
                    if (e.target.value === '+') {
                      setFormData((prev) => ({
                        ...prev,
                        followUpTime: '',
                      }));
                      setDateValue('FollowUpTime');
                    } else {
                      const day = +e.target.value;
                      const dateAfter = new Date();
                      dateAfter.setDate(dateAfter.getDate() + day);
                      const date = new Date(dateAfter);

                      setFormData((prev) => ({
                        ...prev,
                        followUpTime: date,
                      }));
                    }
                  }}
                />
              ),
              'followUp'
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="space-y-3">
              {showSI ? (
                <>
                  {renderFormField(
                    'Sales Inquiry Details',
                    <>
                      <Select
                        placeholder="Select Sales Inquiry"
                        id="salesInquiryDetails"
                        name="salesInquiryDetails"
                        className="w-full"
                        menuPlacement="auto"
                        onChange={(e) => handleChange(e)}
                        disabled={isEdit}
                        value={formData?.SalesInquiryRef}
                        closeMenuOnSelect={true}
                        options={data
                          ?.filter(
                            (item) =>
                              !item.status &&
                              item.ongoingStatus !== 'Quote Created'
                          )
                          .map((el) => ({
                            value: el._id,
                            label: `${el.salesInquiryId} (${el.inquiryName})`,
                          }))}
                      />
                      <MasterDetails
                        isMobile={isMobile}
                        isTablet={isTablet}
                        className="mt-3 p-3 bg-gray-50 rounded border"
                        details={
                          formData?.vendorDetails?.id !== ''
                            ? formData?.vendorDetails
                            : {}
                        }
                        setDetails={setFormData}
                        excludedFields={[
                          'id',
                          '_id',
                          'logo',
                          '__v',
                          'profileId',
                          'additionalFields',
                          'createdAt',
                          'updatedAt',
                        ]}
                        keyToEdit={'vendorDetails'}
                      />
                    </>,
                    'salesInquiryDetails'
                  )}
                </>
              ) : (
                <VendorDetails
                  isMobile={isMobile}
                  isTablet={isTablet}
                  formData={formData}
                  setFormData={setFormData}
                  isEdit={isEdit}
                />
              )}
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="space-y-3">
              <h3 className="text-sm font-semibold text-gray-700">
                Business Details
              </h3>
              {profileSelector()}
              <MasterDetails
                isMobile={isMobile}
                isTablet={isTablet}
                className="p-3 bg-gray-50 rounded border"
                details={transformAddressField(formData?.businessDetails)}
                excludedFields={[
                  'id',
                  '_id',
                  'logo',
                  '__v',
                  'profileId',
                  'createdAt',
                  'updatedAt',
                ]}
                companySelectors={{
                  address: addressSelector,
                  contact: contactSelector,
                  email: emailSelector,
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuoteDetails;
