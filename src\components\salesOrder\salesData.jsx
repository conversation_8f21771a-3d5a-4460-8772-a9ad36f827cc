// import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import {
  CategoryScale,
  Chart as ChartJS,
  Colors,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import { Archive, ArrowDown, ArrowUp, Trash } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import { FaLock } from 'react-icons/fa';
import { useMediaQuery } from 'react-responsive';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import ToolTip from '../../components/global/components/ToolTip';
import {
  generatePrefixId,
  getLocalDate,
  getLocalDateTime,
  handlePdf,
  mobileWidth,
  tabletWidth,
} from '../../helperFunction';
import useDebounceValue from '../../hooks/useDebounceValue';
import { useLazyGetSalesForecastQuery } from '../../slices/ai/forecastApiSlice';
import { useUpdateStatusByFieldNameMutation } from '../../slices/defaultsApiSlice';
import { useGetAllNcrsQuery } from '../../slices/ncrApiSlice';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import {
  useArchiveSalesOrdersMutation,
  useDeleteManySalesOrdersMutation,
  useGetSOFilterOptionsQuery,
  useQuerySalesDataQuery,
  useSendSalesOrderMutation,
  useUpdateSalesOrderMutation,
} from '../../slices/salesOrderSlices';
import { Store } from '../../store/Store';
import {
  PAGINATION_LIMIT,
  SALES_ORDER_FIELDS,
  dateOptions,
} from '../../utils/Constant';
import WithSelectAll from '../../utils/HOC/WithSelectAll';
import { customConfirm } from '../../utils/customConfirm';
import HistorySidebar from '../Kanban/HistorySidebar';
import CustomToolTip from '../global/CustomToolTip';
import TruncateString from '../global/TruncateString';
import Button from '../global/components/Button';
import { FilterIcon, FilterV2 } from '../global/components/FilterV2';
import Header from '../global/components/Header';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Pagination from '../global/components/Pagination';
import RightSidebar from '../global/components/RightSidebar';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import TablePopup from '../global/components/TablePopup';
import MediaModal from '../v3/global/components/MediaModal';
import SendMail from '../v3/global/components/SendMail';
import SalesInfoTab from './SalesInfoTab';
import StatusModal from './StatusModal';
import CreateModal from './createModal';
import SalesOrderSideBar from './salesOrderSideBar';
import SalesReport from './salesReport';

ChartJS.register(
  Colors,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip
);

function getFinalStatus(array) {
  if (array.length === 0) return 'No Job yet';
  if (array.length === 1 && array[0]) return array[0];
  if (array.includes('ongoing')) {
    return 'ongoing';
  } else if (array.every((status) => status === 'complete')) {
    return 'complete';
  } else {
    return 'not started';
  }
}

const MOBILE_VIEW_HEADERS = ['DATE', 'SALES ORDER ID', 'SO STATUS'];
const DESKTOP_VIEW_HEADERS = [
  '',
  'TASK ID',
  'DATE',
  'SALES ORDER ID',
  'QUOTATION ID',
  'COMPANY NAME',
  'DELIVERY DATE',
  'SO STATUS',
  // 'ORDER STATUS',
  'JOB STATUS',
];

/**
 * Renders the SalesData component.
 * This component displays a sales order dashboard with a table of sales orders.
 * It also provides functionality to create, edit, and view details of sales orders.
 */

const SalesData = ({
  handleCheckBoxChange,
  handleSelectAll,
  selectAll,
  checkedRows,
  setCheckedRows,
  rows,
  setRows,
}) => {
  const soStatusFieldName = 'salesOrderCustomStatus';
  let user = JSON.parse(localStorage.getItem('user'))?.user;
  let columnKeys = SALES_ORDER_FIELDS?.map((elem) => elem?.value);
  const handleNavigate = useNavigate();
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  let [searchParams] = useSearchParams();
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });
  // const [sortSales, setSortSales] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  // const [sortDelivery, setSortDelivery] = useState(false);
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [openSideBar, setOpenSideBar] = useState(false);
  const [pos, setPos] = useState(null);
  const [editModal, setEditModal] = useState(false);
  const [createModal, setCreateModal] = useState(
    searchParams.get('kanban') === 'true' ? true : false
  );
  const debounceSearch = useDebounceValue(searchTerm || '');
  const [updateSalesOrder] = useUpdateSalesOrderMutation();
  const [period, setPeriod] = useState(30);
  const [orders, setOrders] = useState([]);
  const [forecastRightSide, setForecastRightSide] = useState(false);
  const [ShowEmailModal, setShowEmailModal] = useState(false);
  const [SendingMail, setSendingMail] = useState(false);
  const [isDeleteVisible, setIsDeleteVisible] = useState(false);
  const [isArchiveVisible, setIsArchiveVisible] = useState(false);
  const [mailData, setMailData] = useState({
    receiver: '',
    body: '',
    subject: '',
    input: {},
    attachments: [],
  });
  const [clickedRow, setClickedRow] = useState('');
  const navigate = useNavigate();
  const [ReadMore, setReadMore] = useState(false);
  const [copyModal, setCopyModal] = useState(false);
  const [deleteManySalesOrders] = useDeleteManySalesOrdersMutation();
  const [archiveSalesOrders] = useArchiveSalesOrdersMutation();
  const [sendSalesOrder] = useSendSalesOrderMutation();
  const { data: ncrs = [] } = useGetAllNcrsQuery();
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [showNotification, setShowNotifications] = useState(false);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [statusToCreate, setStatusToCreate] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [updateSoStatusInDefaults] = useUpdateStatusByFieldNameMutation();
  const [type, setType] = useState('desc');
  const { data: filterOptions } = useGetSOFilterOptionsQuery();
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState([]);
  // const salesStatus = {
  //   pending: 'Pending',
  //   approved: 'Approved',
  //   rejected: 'Rejected',
  // };

  const filterConfig = [
    {
      label: 'Sales Order ID',
      key: 'salesOrderId',
      path: 'salesOrderID',
      type: 'multiSelect',
      options: filterOptions?.salesOrderID,
    },
    {
      key: 'Date',
      label: 'Date',
      path: 'createdAt',
      type: 'date',
    },
    {
      key: 'Delivery Date',
      label: 'Delivery Date',
      path: 'deliveryDate',
      type: 'date',
    },
    {
      key: 'salesOrderStatus',
      label: 'Sales Order Status',
      path: 'salesOrderStatus',
      type: 'multiSelect',
      options: filterOptions?.salesOrderStatus,
    },
    {
      key: 'customer',
      label: 'Customer',
      path: 'customer',
      type: 'multiSelect',
      options: filterOptions?.customer,
    },
    {
      key: 'quotationID',
      label: 'Quotation ID',
      path: 'quotationID',
      type: 'multiSelect',
      options: filterOptions?.quotationID,
    },
  ];

  const colors = {
    PENDING: 'bg-yellow-200 text-yellow-600',
    REJECTED: 'bg-red-200 text-red-600',
    APPROVED: 'bg-[#DCF0DD] text-[#0F6A2E]',
  };

  const defaultStatuses = [
    {
      label: 'Pending',
      value: 'pending',
    },
    {
      label: 'Approved',
      value: 'approved',
    },
    {
      label: 'Rejected',
      value: 'rejected',
    },
  ];

  const getStatusColor = (status) => {
    if (colors[status]) return colors[status];
    return 'bg-fuchsia-200 text-fuchsia-700';
  };

  const periodOptions = [
    { label: '1 Month', value: 30 },
    { label: '3 Months', value: 90 },
    { label: '6 Months', value: 180 },
    { label: '1 Year', value: 360 },
  ];
  const graphOptions = {
    elements: {
      point: {
        radius: 2,
      },
      line: {
        borderWidth: 1.5,
      },
    },
    scales: {
      x: {
        title: { display: true, text: 'Date' },
        grid: {
          display: false,
        },
      },
      y: {
        title: { display: true, text: 'Cost' },
        grid: {
          display: false,
        },
        beginAtZero: true,
      },
    },
  };

  const { data: salesData = {}, isLoading: isLoadingSales } =
    useQuerySalesDataQuery(
      {
        page,
        limit,
        filters,
        debounceSearch,
        type,
      },
      { skip: !page || !limit, refetchOnMountOrArgChange: true }
    );
  const { results = [], totalPages = 0, totalResults = 1 } = salesData;

  const {
    state,
    defaults: { defaultParam },
  } = useContext(Store);

  useEffect(() => {
    if (results) {
      setRows(results);
    } else {
      setRows([]);
    }
  }, [results, setRows]);

  useEffect(() => {
    if (results.length > 0) {
      setOrders(results);
    }
  }, [results, setOrders]);

  const corresponding = { 'Not Assigned': 'Not Assigned' };

  const ordersMapping = {};
  orders?.forEach((order) => {
    const status = order.workOrders.map((el) => corresponding[el.value]);
    ordersMapping[order._id] = getFinalStatus(status);
  });

  const [
    salesForecasting,
    { data: forecastSalesData = {}, isLoading: forecastLoading },
  ] = useLazyGetSalesForecastQuery();

  const handleSalesForecast = (period) => {
    salesForecasting(period)
      .unwrap()
      .then(() => {
        setForecastRightSide(true);
      });
  };
  const sideBarHandler = (e, pos) => {
    setPos(pos);
    // prevent opening sidebar when checkbox is checked
    if (e.target.tagName.toLowerCase() !== 'input') setOpenSideBar(true);
  };

  const editModalHandler = () => {
    setEditModal(!editModal);
  };

  const createModalHandler = () => {
    setCreateModal(!createModal);
  };

  const handleDeleteAll = async () => {
    const idsToDelete = checkedRows.map((item) => item._id);
    const res = await deleteManySalesOrders({ ids: idsToDelete });
    if (res?.data?.message) {
      const { msg1, msg2 } = res.data.message;

      if (msg1 && msg2) {
        toast.error(msg1);
        toast.success(msg2);
      } else if (msg1) {
        toast.error(msg1);
      } else {
        toast.success(msg2);
      }
    }

    setCheckedRows([]);
  };

  const handleArchiveAll = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to archive these Sales Orders?',
      'success'
    );
    if (!confirm) return;
    try {
      const idsToArchive = checkedRows.map((item) => item._id);
      const res = await archiveSalesOrders({ ids: idsToArchive });
      if (res) {
        toast.success('Sales Orders archived successfully');
        setCheckedRows([]);
      }
    } catch (err) {
      toast.error(err?.data?.message || err?.message || 'Something went wrong');
    }
  };

  const handleSendmail = async () => {
    setSendingMail(true);
    const fd = new FormData();
    fd.append('receiver', mailData.receiver);
    fd.append('id', clickedRow);
    fd.append('subject', mailData.subject);
    fd.append(
      'input',
      JSON.stringify({
        ...rows[pos],
      })
    );
    fd.append('id', clickedRow);
    if (mailData?.attachments?.length !== 0) {
      mailData.attachments.forEach((file, index) => {
        // Convert the file object to a JSON string
        fd.append(`attachments[${index}][data]`, file.data);
        fd.append(`attachments[${index}][name]`, file.name);
        fd.append(`attachments[${index}][type]`, file.type);
      });
    }
    fd.append('body', mailData.body);
    const res = await sendSalesOrder(fd);
    setSendingMail(false);
    if (res) {
      toast.success('Mail Sent Successfully');
    }
    setShowEmailModal(false);
    setMailData({
      receiver: '',
      body: '',
      subject: '',
      input: {},
      attachments: [],
    });
  };

  const isPIDisabled = checkedRows?.at(0)?.salesOrderStatus !== 'approved';
  useEffect(() => {
    if (checkedRows?.length > 0) {
      setIsDeleteVisible(true);
      setIsArchiveVisible(true);
    } else {
      setIsDeleteVisible(false);
      setIsArchiveVisible(false);
    }
  }, [checkedRows]);

  useEffect(() => {
    if (orders?.length === 0) return;
    if (searchParams.get('navigateTo') && searchParams.get('editid')) {
      const orderToEdit = orders?.findIndex((order) => {
        return order?._id === searchParams.get('editid');
      });
      setPos(orderToEdit);
      setEditModal(true);
    }
  }, [searchParams, orders]);

  useEffect(() => {
    if (searchParams.get('createModal') === 'true') {
      setCreateModal(true);
    }
  }, [searchParams]);

  const setSteps = (taskId) => {
    let tasks = state?.allTiles;
    let chosenTask = tasks?.find((elem) => elem?.taskId === taskId);
    if (chosenTask) {
      setHistorySidebar({
        open: true,
        steps: chosenTask?.steps,
        orderId: chosenTask?._id,
      });
    }
  };

  const getSOStatus = () => {
    const allCustomStatuses = defaultParam?.[soStatusFieldName];
    return [...defaultStatuses, ...(allCustomStatuses || [])];
  };

  const handleCreateStatus = async () => {
    if (!statusToCreate) {
      toast.error('Please enter a status');
      return;
    }
    const allStatuses = getSOStatus();
    const alreadyExists = allStatuses?.find(
      (status) => status?.label === statusToCreate
    );
    if (alreadyExists) {
      toast.error('Status already exists');
      return;
    }
    const body = {
      fieldName: soStatusFieldName,
      value: [
        ...(allStatuses || []),
        { label: statusToCreate.trim(), value: statusToCreate.trim() },
      ],
    };
    const res = await updateSoStatusInDefaults(body);
    if (!res?.error) {
      toast.success('Status created successfully');
      setStatusToCreate('');
    }
  };

  const handleDeleteCustomStatus = async (statusToDelete) => {
    const allStatuses = getSOStatus()?.filter(
      (status) => status.label !== statusToDelete.label
    );
    const body = {
      fieldName: soStatusFieldName,
      value: [...(allStatuses || [])],
    };
    const res = await updateSoStatusInDefaults(body);
    if (!res?.error) {
      toast.success('Status deleted successfully');
      setSelectedStatus('');
    }
  };

  return (
    <>
      {isMobile && clickedRow && (
        <TablePopup
          isEdit={false}
          onDownload={() => handlePdf(getPdf, clickedRow?._id, 'salesOrder')}
          downloading={isFetchingPdf}
          onBack={() => setClickedRow(null)}
        >
          <div className="space-y-4 !text-[12px]">
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">TASK ID</label>
              <p>
                {clickedRow?.taskId?.customTaskId
                  ? `${clickedRow?.taskId?.customTaskId}(${clickedRow?.taskId?.taskId})`
                  : clickedRow?.taskId?.taskId
                    ? clickedRow?.taskId?.taskId
                    : '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">SALES ORDER DATE</label>
              <p>{getLocalDateTime(clickedRow?.salesOrderDate)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">SALES ORDER ID</label>
              <p>{clickedRow?.salesOrderID}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">QUOTATION ID</label>
              <p>{clickedRow?.quotationID}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">CUSTOMER NAME</label>
              <p>{clickedRow?.customer}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">DELIVERY DATE</label>
              <p>{getLocalDateTime(clickedRow?.deliveryDate)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">SALES ORDER STATUS</label>
              <p>
                {!defaultParam?.projectDefaults?.disableApprovals ? (
                  <span
                    className={`${
                      colors[
                        clickedRow?.salesOrderStatus?.charAt(0).toUpperCase() +
                          clickedRow?.salesOrderStatus?.slice(1)
                      ]
                    } px-3 py-1 rounded-md font-medium whitespace-nowrap`}
                  >
                    {clickedRow?.salesOrderStatus?.charAt(0).toUpperCase() +
                      clickedRow?.salesOrderStatus?.slice(1)}
                  </span>
                ) : (
                  <span>-</span>
                )}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">ORDER STATUS</label>
              <p>{ordersMapping[clickedRow?._id]}</p>
            </div>
          </div>
        </TablePopup>
      )}
      <StatusModal
        openModal={openStatusModal}
        setModalOpen={setOpenStatusModal}
        statusToCreate={statusToCreate}
        setStatusToCreate={setStatusToCreate}
        handleCreateStatus={handleCreateStatus}
        getSOStatus={getSOStatus}
        selectedStatus={selectedStatus}
        setSelectedStatus={setSelectedStatus}
        handleDeleteCustomStatus={handleDeleteCustomStatus}
        defaultStatuses={defaultStatuses}
        clickedRow={clickedRow}
      />
      <SalesReport className="absolute" data={rows[pos]} />
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      <div className="w-full flex justify-between items-center">
        <div className="flex flex-col !min-w-[30rem]">
          {!createModal && !editModal && !copyModal && (
            <Header
              description=""
              title="Sales Order Dashboard"
              infoTitle="Welcome to the Sales Order Page"
              infoDesc="Your hub for generating new Sales Order with ease. The Sales Order page simplifies the process of creating and managing
            Sales Order."
              paras={[
                'The Sales Order page simplifies the process of creating and managing Sales Order. Quickly generate, review, and track Sales Order with ease, enhancing your Sales Order efficiency. Easily communicate pricing details and streamline your Sales Order workflow.',
              ]}
            />
          )}
        </div>
      </div>
      {!editModal && !createModal && !copyModal && (
        <SalesInfoTab isMobile={isMobile} />
      )}

      {editModal ? (
        <div
          className={`${!isMobile && !isTablet && 'mx-48 !bg-white border-x-2 px-6'}`}
        >
          <CreateModal
            isMobile={isMobile}
            isTablet={isTablet}
            isEdit={editModal}
            setIsAdd={setCreateModal}
            setIsEdit={setEditModal}
            edit={true}
            orders={rows}
            {...rows[pos]}
            modalHandler={editModalHandler}
            // workOrderData={workOrderData}
            defaultParam={defaultParam}
          />
        </div>
      ) : createModal ? (
        <div
          className={`${!isMobile && !isTablet && 'mx-48 !bg-white border-x-2 px-6'}`}
        >
          <CreateModal
            isMobile={isMobile}
            isTablet={isTablet}
            isAdd={createModal}
            setIsAdd={setCreateModal}
            setIsEdit={setEditModal}
            orders={rows}
            modalHandler={createModalHandler}
            newId={defaultParam?.salesOrderId}
            // workOrderData={workOrderData}
            defaultParam={defaultParam}
            ncr={ncrs}
          />
        </div>
      ) : copyModal ? (
        <div
          className={`${!isMobile && !isTablet && 'mx-48 !bg-white border-x-2 px-8'}`}
        >
          <CreateModal
            isMobile={isMobile}
            isTablet={isTablet}
            isEdit={copyModal}
            setIsAdd={setCreateModal}
            setIsEdit={setCopyModal}
            edit={true}
            orders={rows}
            {...rows[pos]}
            modalHandler={editModalHandler}
            // workOrderData={workOrderData}
            defaultParam={defaultParam}
            salesOrderStatus={'pending'}
            isCopy={true}
          />
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center w-full bg-white rounded-tl-lg rounded-tr-lg mt-2 p-2">
            {!isMobile && (
              <p
                className={`text-[#005EEC] text-[15px] ${isTablet && 'text-xs'} font-medium hover:underline cursor-pointer  !ml-5 !min-w-[9rem]`}
                onClick={() => setShowNotifications(!showNotification)}
              >
                {showNotification ? 'Hide' : 'Show'} Notification
              </p>
            )}
            <div className={`relative flex items-center w-full`}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5 absolute left-3 ml-2 text-gray-400 cursor-pointer"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                />
              </svg>

              <input
                className={`${isMobile ? ' !w-[140px] ml-3 mr-1 !pl-8 text-[10px]' : isTablet ? '!min-w-[50px] !pl-10 ml-3 w-[118px]' : '!min-w-[100px] !pl-10 ml-3 w-[400px]'} !rounded-3xl !px-5 !py-1.5 outline-none md:text-sm  bg-[#F2F1FB]`}
                placeholder={`${!isMobile ? 'Search Items from list...' : 'Search Items...'}`}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex items-center justify-end mb-1 w-full mr-2 gap-4">
              {user?.archiveSalesOrderManagement && (
                <CustomToolTip
                  tooltipId="archive-tooltip"
                  content="Archive"
                  place="top"
                  effect="solid"
                  className={`bg-black text-white p-1 rounded ${isArchiveVisible ? '' : 'hidden'}`}
                >
                  <Archive
                    className={`h-5 w-5 ${isArchiveVisible ? 'text-blue-500  cursor-pointer ' : 'text-gray-400 cursor-not-allowed'}`}
                    onClick={handleArchiveAll}
                  />
                </CustomToolTip>
              )}
              {!isMobile && (
                <CustomToolTip
                  tooltipId="delete-tooltip"
                  content="Delete"
                  place="top"
                  effect="solid"
                  className={`bg-black text-white p-1 rounded ${isDeleteVisible ? '' : 'hidden'}`}
                >
                  <Trash
                    className={`w-5 h-5 ${isDeleteVisible ? 'cursor-pointer text-red-500' : 'opacity-50 cursor-not-allowed'}`}
                    onClick={handleDeleteAll}
                  />
                </CustomToolTip>
              )}
              {!isMobile && (
                <div className="flex gap-5">
                  {!createModal && !editModal && !copyModal && (
                    <FilterIcon
                      showFilters={showFilters}
                      setShowFilters={setShowFilters}
                    />
                  )}
                </div>
              )}
              {!isMobile && (
                <Button
                  className={`!p-1 !px-2 !text-[12px] !h-6 ${isTablet && 'text-xs'}`}
                  onClick={() =>
                    handleNavigate(
                      `/accountmanagement/invoices/create/sales?returnTab=sales&salesOrderId=${checkedRows[0]?._id}`
                    )
                  }
                  disabled={isPIDisabled}
                >
                  Convert to SI
                </Button>
              )}
              {!createModal && !editModal && !copyModal && (
                <Button
                  className={`!p-1 !px-2 !text-[12px] !h-6 ${isTablet && 'text-xs'}`}
                  onClick={() => handleSalesForecast(period)}
                >
                  Forecast
                </Button>
              )}
              {!createModal && !editModal && !copyModal && (
                <div className=" !mr-3">
                  <Button
                    className={'!px-2 !py-1 !h-6 !text-xs'}
                    onClick={createModalHandler}
                  >
                    +&nbsp;Create
                  </Button>
                </div>
              )}
            </div>
          </div>
          {showNotification ? (
            <div>
              {ncrs.filter((notif) => notif.orderCheck).length === 0 ? (
                <div>No notifications</div>
              ) : (
                <div className="mb-3">
                  {ncrs
                    .filter((notif) => notif.orderCheck)
                    .map((notif) => (
                      <div
                        className={`flex items-center justify-between px-[1.3rem] py-[0.8rem] border shadow-md rounded-[1rem] mb-3 cursor-pointer`}
                        key={notif._id}
                      >
                        <div className="flex items-center gap-x-5">
                          <div
                            className={`color-box w-[1rem] h-[1rem] rounded-md`}
                            style={{ backgroundColor: '#7f04e4' }}
                          ></div>
                          <div className="details">
                            <p className="font-medium text-[12px]">
                              <span>New NCR {notif?.id || ''} is Raised</span>
                            </p>
                            <span className="text-slate-500 text-[12px]">
                              Raised on: {getLocalDate(notif.createdAt)}
                            </span>
                            <p className="text-slate-500 font-medium text-[14px]"></p>
                          </div>
                        </div>
                        <div className="button">
                          <Button
                            className="!rounded-2xl"
                            onClick={() => {
                              let taskId = generatePrefixId(
                                defaultParam?.prefixIds?.['taskId']
                              );
                              navigate(
                                `/salesordermanagement/orders?kanban=true&department=sales&page=Sales%20Order&index=2&refType=Sales%20Order&taskID=${taskId}&ncrId=${notif._id}`
                              );
                              setCreateModal(true);
                            }}
                          >
                            + Create Sales Order
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>
          ) : (
            <>
              <FilterV2
                showFilters={showFilters}
                config={filterConfig}
                setFilters={setFilters}
              />
              <Table>
                <Table.Head>
                  <Table.Row>
                    {DESKTOP_VIEW_HEADERS.map((el, index) => {
                      const isHide =
                        isMobile && !MOBILE_VIEW_HEADERS.includes(el);
                      if (el === '' && !isMobile) {
                        return (
                          <Table.Th key={index}>
                            {checkedRows.length > 0 ? (
                              <div>
                                <input
                                  type="checkbox"
                                  className="mr-2"
                                  checked={selectAll}
                                  onChange={(e) => handleSelectAll(e)}
                                />
                                Select All
                              </div>
                            ) : (
                              ''
                            )}
                          </Table.Th>
                        );
                      }

                      if (el === 'DATE') {
                        return (
                          <Table.Th key={index}>
                            <div className="flex">
                              <div>Date</div>
                              {type === 'asc' ? (
                                <ArrowUp
                                  cursor={'pointer'}
                                  size={15}
                                  onClick={() => {
                                    setType('desc');
                                  }}
                                />
                              ) : (
                                <ArrowDown
                                  cursor={'pointer'}
                                  size={15}
                                  onClick={() => {
                                    setType('asc');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }
                      return !isHide && <Table.Th key={index}>{el}</Table.Th>;
                    })}
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {isLoadingSales ? (
                    <Table.Row>
                      <Table.Td colSpan={DESKTOP_VIEW_HEADERS.length + 1}>
                        <div className="flex justify-center items-center">
                          <Spin
                            size="large"
                            indicator={<LoadingOutlined spin />}
                          />
                        </div>
                      </Table.Td>
                    </Table.Row>
                  ) : (
                    rows?.map((order, pos) => {
                      return (
                        <Table.Row
                          key={order?.salesOrderID}
                          isClickable={isMobile}
                          onClick={() => setClickedRow(order)}
                          className=" hover:bg-gray-100 hover:cursor-pointer"
                        >
                          {!isMobile && (
                            <Table.Td>
                              <input
                                type="checkbox"
                                onChange={(event) => {
                                  handleCheckBoxChange(event, order);
                                }}
                                checked={checkedRows.includes(order)}
                              />
                            </Table.Td>
                          )}
                          {!isMobile && (
                            <Table.Td
                              className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                              onClick={() => {
                                setSteps(order?.taskId?.taskId);
                              }}
                            >
                              {order?.taskId?.customTaskId
                                ? `${order?.taskId?.customTaskId.slice(0, 20)}(${order?.taskId?.taskId})...`
                                : order?.taskId?.taskId
                                  ? order?.taskId?.taskId
                                  : '-'}
                            </Table.Td>
                          )}

                          {user?.columnAccess?.includes(columnKeys?.[2]) ? (
                            <Table.Td>
                              {new Date(
                                order?.salesOrderDate
                              ).toLocaleDateString('en-in', dateOptions)}
                            </Table.Td>
                          ) : (
                            <Table.Td>
                              <div className="h-[3rem] w-full flex items-center">
                                <FaLock className="text-2xl text-slate-400" />
                              </div>
                            </Table.Td>
                          )}

                          <Table.Td
                            className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                            isClickable={isMobile}
                            onClick={(e) => {
                              if (isMobile) {
                                setClickedRow(order);
                              } else {
                                sideBarHandler(e, pos);
                              }
                            }}
                          >
                            <TruncateString length={13}>
                              {order?.salesOrderID}
                            </TruncateString>
                          </Table.Td>
                          {!isMobile && (
                            <>
                              {user?.columnAccess?.includes(columnKeys?.[1]) ? (
                                <Table.Td>{order?.quotationID}</Table.Td>
                              ) : (
                                <Table.Td>
                                  <div className="h-[3rem] w-full flex items-center ml-[2rem]">
                                    <FaLock className="text-2xl text-slate-400" />
                                  </div>
                                </Table.Td>
                              )}
                            </>
                          )}
                          {!isMobile && (
                            <>
                              {user?.columnAccess?.includes(columnKeys?.[1]) ? (
                                <Table.Td>
                                  {order?.CustomerData?.company_name ? (
                                    order.CustomerData?.company_name?.length <=
                                    13 ? (
                                      order?.CustomerData?.company_name
                                    ) : (
                                      <ToolTip
                                        text={order.CustomerData?.company_name}
                                      >
                                        {order.CustomerData?.company_name.substring(
                                          0,
                                          13
                                        ) + '...'}
                                      </ToolTip>
                                    )
                                  ) : (
                                    '-'
                                  )}
                                </Table.Td>
                              ) : (
                                <Table.Td>
                                  <div className="h-[3rem] w-full flex items-center ml-[2rem]">
                                    <FaLock className="text-2xl text-slate-400" />
                                  </div>
                                </Table.Td>
                              )}
                            </>
                          )}
                          {!isMobile && (
                            <>
                              {user?.columnAccess?.includes(columnKeys?.[3]) ? (
                                <Table.Td>
                                  {order?.deliveryDate
                                    ? new Date(
                                        order.deliveryDate
                                      ).toLocaleDateString('en-in', dateOptions)
                                    : '-'}
                                </Table.Td>
                              ) : (
                                <Table.Td>
                                  <div className="h-[3rem] w-full flex items-center ml-[2rem]">
                                    <FaLock className="text-2xl text-slate-400" />
                                  </div>
                                </Table.Td>
                              )}
                            </>
                          )}
                          {user?.columnAccess?.includes(columnKeys?.[0]) ? (
                            <Table.Td>
                              {!defaultParam?.projectDefaults
                                ?.disableApprovals ? (
                                <div className="flex items-center">
                                  <span
                                    onClick={() => {
                                      setClickedRow(order);
                                      setOpenStatusModal(true);
                                    }}
                                    className={`${getStatusColor(
                                      order?.salesOrderStatus
                                        ?.toUpperCase()
                                        ?.trim()
                                    )} px-3 py-1 rounded-full font-medium whitespace-nowrap`}
                                  >
                                    {order?.salesOrderStatus
                                      ?.charAt(0)
                                      .toUpperCase() +
                                      order?.salesOrderStatus?.slice(1)}
                                  </span>
                                  {order?.salesOrderStatus?.toLowerCase() ===
                                    'rejected' &&
                                    [...(order?.statusTimeline || [])].sort(
                                      (a, b) =>
                                        new Date(b.timestamp).getTime() -
                                        new Date(a.timestamp).getTime()
                                    )?.[0]?.remark && (
                                      <InfoTooltip
                                        id="quote status"
                                        position="top"
                                        className="ml-2"
                                      >
                                        {
                                          [
                                            ...(order?.statusTimeline || []),
                                          ].sort(
                                            (a, b) =>
                                              new Date(b.timestamp).getTime() -
                                              new Date(a.timestamp).getTime()
                                          )?.[0]?.remark
                                        }
                                      </InfoTooltip>
                                    )}
                                </div>
                              ) : (
                                <span>-</span>
                              )}
                            </Table.Td>
                          ) : (
                            <Table.Td>
                              <div className="h-[3rem] w-full flex items-center ml-[1.5rem]">
                                <FaLock className="text-2xl text-slate-400" />
                              </div>
                            </Table.Td>
                          )}

                          {/* <Table.Td>{salesStatus[order?.salesOrderStatus]}</Table.Td> */}
                          {!isMobile && (
                            <Table.Td>{ordersMapping[order?._id]}</Table.Td>
                          )}
                          {/* {order.salesOrderStatus === 'approved' ? (
                      <Table.Options onView={(e) => sideBarHandler(e, pos)} />
                    ) : (
                      <Table.Options
                        onView={(e) => sideBarHandler(e, pos)}
                        onApproveSalesOrder={async () => {
                          if (
                            await customConfirm(
                              `Once a Sales Order is Approved, it cannot be edited or deleted! 
          Are you sure you want to Approve ${order?.salesOrderID}`,
                              'success'
                            )
                          ) {
                            const res = await updateSalesOrder({
                              data: {
                                ...order,
                                salesOrderStatus: 'approved',
                              },
                              id: order._id,
                            }).unwrap();
                            if (res) {
                              toast.success('Sales Order Approved', {
                                theme: 'colored',
                                position: 'top-right',
                                toastId: 'Sales Order Approved',
                              });
                            } else {
                              toast.error('Error in Sales Order Approval', {
                                theme: 'colored',
                                position: 'top-right',
                                toastId: 'Sales Order not Approved',
                              });
                            }
                          } else {
                            return;
                          }
                        }}
                        onRejectSalesOrder={async () => {
                          const res = await updateSalesOrder({
                            data: {
                              ...order,
                              salesOrderStatus: 'rejected',
                            },
                            id: order._id,
                          }).unwrap();
                          if (res) {
                            toast.success('Sales Order Rejected', {
                              theme: 'colored',
                              position: 'top-right',
                              toastId: 'Sales Order Rejected',
                            });
                          } else {
                            toast.error('Error in Sales Order Rejection', {
                              theme: 'colored',
                              position: 'top-right',
                              toastId: 'Sales Order not Rejected',
                            });
                          }
                        }}
                      />
                    )} */}
                        </Table.Row>
                      );
                    })
                  )}
                </Table.Body>
              </Table>
              <Pagination
                limit={limit}
                page={page}
                totalPages={totalPages}
                totalResults={totalResults}
                setPage={setPage}
                setLimit={setLimit}
                className={`w-full`}
              />
            </>
          )}
        </>
      )}
      {ReadMore && (
        <MediaModal
          FormData={rows[pos]?.files}
          isView={true}
          setShowModal={setReadMore}
          ShowModal={ReadMore}
        />
      )}
      {results.length > 0 ? (
        <RightSidebar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          scale={736}
        >
          <SalesOrderSideBar
            corresponding={ordersMapping}
            setCopyModal={setCopyModal}
            data={rows}
            pos={pos}
            setPos={setPos}
            openSideBar={openSideBar}
            setOpenSideBar={setOpenSideBar}
            editModal={editModal}
            editModalHandler={setEditModal}
            updateSalesOrder={updateSalesOrder}
            setShowEmailModal={setShowEmailModal}
            setReadMore={setReadMore}
            setClickedRow={setClickedRow}
          />
        </RightSidebar>
      ) : null}
      <RightSidebar
        scale={736}
        openSideBar={forecastRightSide}
        setOpenSideBar={setForecastRightSide}
      >
        {forecastLoading && <Spinner />}
        {forecastSalesData?.[0]?.labels && (
          <>
            <h3 className="p-2">Sales Forecasting</h3>
            <div className="flex w-full justify-end">
              <div>
                <Select
                  options={periodOptions}
                  onChange={(e) => {
                    setPeriod(e.target.value);
                    handleSalesForecast(e.target.value);
                  }}
                  value={period}
                />
              </div>
            </div>
            <Line
              className="mb-6"
              options={graphOptions}
              data={forecastSalesData[0] || {}}
            />
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Forecast Date</Table.Th>
                  <Table.Th>Sales amount expected</Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                <Table.Row>
                  <Table.Td>{forecastSalesData[1]}</Table.Td>
                  <Table.Td>{forecastSalesData[2]}</Table.Td>
                </Table.Row>
              </Table.Body>
            </Table>
          </>
        )}
      </RightSidebar>
      {ShowEmailModal && (
        <SendMail
          title={'Send Sales Order Mail'}
          mailData={mailData}
          setMailData={setMailData}
          SendingMail={SendingMail}
          setShowEmailModal={setShowEmailModal}
          handleSendmail={handleSendmail}
        />
      )}
    </>
  );
};

export default WithSelectAll(SalesData);
