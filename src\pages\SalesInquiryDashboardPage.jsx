import { useEffect, useRef, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { useLocation, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import Input from '../components/global/components/Input';
import Modal from '../components/global/components/Modal';
import Pagination from '../components/global/components/Pagination';
import Select from '../components/global/components/Select';
import EditSalesInquiryModal from '../components/SalesInquiryDashboard/EditSalesInquiryModal';
import SalesInquiryHeader from '../components/SalesInquiryDashboard/SalesInquiryHeader';
import SalesInquiryInfoTab from '../components/SalesInquiryDashboard/SalesInquiryInfoTab';
import SalesInquiryModal from '../components/SalesInquiryDashboard/SalesInquiryModal';
import SalesInquiryTable from '../components/SalesInquiryDashboard/SalesInquiryTable';
import { mobileWidth, tabletWidth } from '../helperFunction';
import useDebounceValue from '../hooks/useDebounceValue';
import { useAddAdditionalColumnsMutation } from '../slices/columnsApiSlice';
import { useGetSalesInquiryPagesQuery } from '../slices/salesInquiryDashboardApiSlice';
import { PAGINATION_LIMIT } from '../utils/Constant';

function SalesInquiryPage() {
  const [field, setField] = useState('createdAt');
  const [type, setType] = useState('desc');
  const [rows, setRows] = useState([]); 
  const [searchParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
    customerId: null,
  });

  /* The code is retrieving the values of the `page` and `limit` parameters from the URL search
 parameters using the `searchParams` object. The `+` operator is used to convert the retrieved
 values to numbers. */
  const page = +searchParams?.get('page') || 1;
  const limit = +searchParams?.get('limit') || PAGINATION_LIMIT;
  const isMobile = useMediaQuery({ query: mobileWidth });
  const [isAdd, setIsAdd] = useState(
    searchParams.get('kanban') === 'true' ? true : false
  );
  const [filters, setFilters] = useState([]);
  const [showFilters, setShowFilters] = useState(false);

  const isTablet = useMediaQuery({ query: tabletWidth });
  const [searchTerm, setSearchTerm] = useState('');
  const debounceSearch = useDebounceValue(searchTerm || '');
  const [isEdit, setIsEdit] = useState(false);
  const [editSalesInquiryData, setEditSalesInquiryData] = useState();
  const { data = {}, isLoading } = useGetSalesInquiryPagesQuery(
    { limit, page, debounceSearch, type, field, filters },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );
  const { results = [], totalPages = 0, totalResults = 0 } = data;
  const location = useLocation();
  const pathRef = useRef(location.pathname);
  const [customer, setCustomer] = useState(null);
  const [addColumns, setAddColumns] = useState(false);
  const [addAdditionalColumns] = useAddAdditionalColumnsMutation();
  const [columnData, setColumnData] = useState({
    fieldName: '',
    fieldType: null,
  });
  const [isCopy, setIsCopy] = useState(false);
  const [copyData, setCopyData] = useState({});

  useEffect(() => {
    pathRef.current = location.pathname;
  }, [location.pathname]);

  useEffect(() => {
    const customerId = searchParams.get('customerId');
    if (customerId && customerId !== 'null') {
      setCustomer(customerId);
      setIsAdd(true);
    }
  }, [searchParams]);

  const handleSubmit = async () => {
    if (columnData.fieldName === '' || columnData.fieldType === null) {
      toast.error('Check conditions');
      return;
    }
    const added = await addAdditionalColumns({
      data: columnData,
      path: pathRef.current,
    });
    if (added) {
      toast.success('Column added');
      setAddColumns(false);
      setColumnData({
        fieldName: '',
        fieldType: null,
      });
    }
  };

  return (
    <div className="w-full h-full">
      {!isAdd && !isEdit && !isCopy && (
        <SalesInquiryHeader
          setIsAdd={setIsAdd}
          isAdd={isAdd}
          isEdit={isEdit}
          setAddColumns={setAddColumns}
        />
      )}
      {!isAdd && !isEdit && !isCopy && (
        <SalesInquiryInfoTab isMobile={isMobile} />
      )}
      {isAdd ? (
        <SalesInquiryModal
          totalResults={totalResults}
          setIsAdd={setIsAdd}
          customerID={customer}
          setIsCopy={setIsCopy}
          isTablet={isTablet}
          isMobile={isMobile}
          isAdd={isAdd}
          isEdit={isEdit}
          setAddColumns={setAddColumns}
        />
      ) : isEdit ? (
        <EditSalesInquiryModal
          setIsEdit={setIsEdit}
          salesInquiryData={editSalesInquiryData}
        />
      ) : isCopy ? (
        <SalesInquiryModal
          copyData={copyData}
          totalResults={totalResults}
          customerID={customer}
          setIsAdd={setIsAdd}
          isCopy={true}
          setIsCopy={setIsCopy}
        />
      ) : (
        <>
          <section className="w-full overflow-x-auto mt-1">
            <SalesInquiryTable
              setField={setField}
              type={type}
              setType={setType}
              data={results}
              setIsEdit={setIsEdit}
              setEditSalesInquiryData={setEditSalesInquiryData}
              setIsCopy={setIsCopy}
              setCopyData={setCopyData}
              field={field}
              setRows={rows}
              setSearchTerm={setSearchTerm}
              setIsAdd={setIsAdd}
              isAdd={isAdd}
              isEdit={isEdit}
              isMobile={isMobile}
              setFilters={setFilters}
              setShowFilters={setShowFilters}
              showFilters={showFilters}
              isLoading={isLoading}
            />
          </section>

          <Pagination
            limit={limit}
            page={page}
            totalPages={totalPages}
            totalResults={totalResults}
            setPage={(e) =>
              setSearchParams(
                (prev) => {
                  prev.set('page', e);
                  return prev;
                },
                { replace: true }
              )
            }
            setLimit={(e) =>
              setSearchParams(
                (prev) => {
                  prev.set('limit', e);
                  return prev;
                },
                { replace: true }
              )
            }
          />
        </>
      )}
      {addColumns && (
        <Modal
          title={'Add Columns'}
          onCloseModal={() => {
            setAddColumns(false);
            setColumnData({
              fieldType: null,
              fieldName: '',
            });
          }}
          onSubmit={() => handleSubmit()}
        >
          {() => {
            return (
              <div>
                <div>
                  <label>Field Name</label>
                  <Input
                    type="text"
                    value={columnData?.fieldName}
                    onChange={(e) =>
                      setColumnData((prev) => {
                        return {
                          ...prev,
                          fieldName: e.target.value,
                        };
                      })
                    }
                  />
                </div>
                <div>
                  Field Type
                  <Select
                    options={[
                      { value: 'Input', label: 'Input' },
                      { value: 'Check', label: 'Check' },
                      { value: 'Date', label: 'Date' },
                    ]}
                    onChange={(e) =>
                      setColumnData((prev) => {
                        return {
                          ...prev,
                          fieldType: e.target.value,
                        };
                      })
                    }
                    value={columnData.fieldType}
                  />
                </div>
              </div>
            );
          }}
        </Modal>
      )}
    </div>
  );
}

export default SalesInquiryPage;
